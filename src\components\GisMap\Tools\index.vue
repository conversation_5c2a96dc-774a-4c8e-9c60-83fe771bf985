<template>
  <div class="component-tools"
       :class="showModel === 'gas_model' ||
  showModel === 'drainage_model' ||
  showModel === 'heating_model'? 'model-layout': '' ">
    <div class="tool-left">
      <div class="tool-left-top">
          <el-tooltip
             v-for="item in leftTopTools"
             :key="item.id"
             :content="item.label"
             placement="left"
             popper-class="gis-tool-tooltip"
          >
              <div
                 class="tool-item-btn"
                 :class="{ active: item.id === leftActiveId }"
                 @click="handleLeftSelectTool(item)"
              >
                <img :src="item.icon" alt="" />
              </div>
          </el-tooltip>
          <GasModel v-if="showModel === 'gas_model'"/>
          <DrainageModel v-if="showModel === 'drainage_model'"/>
          <HeatingModel v-if="showModel === 'heating_model'"/>
      </div>
      <Legend v-if="showLegend" />
    </div>
    <div class="tool-right">
      <div class="tool-right-top">
        <div class="tool-item" v-for="item in topTools" :key="item.id">
          <el-popover
            placement="left-start"
            :width="'auto'"
            :show-arrow="false"
            popper-class="gis-tool-popover"
            :visible="item.id === topActiveId"
            v-if="!!item.component"
          >
            <template #reference>
              <div>
                <el-tooltip
                  :content="item.label"
                  placement="left"
                  popper-class="gis-tool-tooltip"
                >
                  <div
                    class="tool-item-btn"
                    :class="{ active: item.id === topActiveId }"
                    @click="handleTopSelectTool(item)"
                  >
                    <img :src="item.icon" alt="" />
                  </div>
                </el-tooltip>
              </div>
            </template>
            <component :is="item.component" />
          </el-popover>
          <el-tooltip
            v-else
            :content="item.label"
            placement="left"
            popper-class="gis-tool-tooltip"
          >
            <div
              class="tool-item-btn"
              :class="{active: item.id === topActiveId}"
              @click="handleTopSelectTool(item)"
            >
              <img :src="item.icon" alt="" />
            </div>
          </el-tooltip>
        </div>
      </div>
      <div class="tool-right-bottom">
        <el-tooltip
          v-for="item in bottomTools"
          :key="item.id"
          :content="item.label"
          placement="left"
          popper-class="gis-tool-tooltip"
        >
          <div
            class="tool-item-btn"
            :class="{
              active: item.id === '7' ? showLegend : item.id === bottomActiveId,
            }"
            @click="handleBottomSelectTool(item)"
          >
            <img :src="item.icon" alt="" />
          </div>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup>
import {computed, reactive, ref, watch} from "vue";
import GasModel from "./components/GasModel/index.vue";
import DrainageModel from "./components/DrainageModel/index.vue";
import HeatingModel from "./components/HeatingModel/index.vue";
import Legend from "./components/Legend/index.vue";
import LayerTree from "./components/LayerTree/index.vue";
import Search from "./components/Search/index.vue";
import EyesEarth from "./components/EyesEarth/index.vue";
import BottomMap from "./components/BottomMap/index.vue";
import { mapStates } from "@/components/GisMap/mapStates";
import bus from "@/utils/mitt";
import { useRoute } from "vue-router";
const route = useRoute();
const leftActiveId = ref("");
const topActiveId = ref("");
const bottomActiveId = ref("");
const showLegend = ref(false);
const showPane = ref(true);
const showModel = ref('');

const state = reactive({
    mapType: 'map3d', //地图类型: 电子地图-vector、影像地图-image、气象云图-satellite、气象雷达-radar、三维建模-map3d
});

bus.on('changeBottomMapType', (type) => {
    state.mapType = type;
});

watch(
  () => route,
  (val) => {
    leftActiveId.value = "";
    topActiveId.value = "";
    bottomActiveId.value = "";
    showLegend.value = false;
    showPane.value = true;
    showModel.value = '';
    if (route.path ==="/gas/decision-screen") {
      showModel.value = 'gas_model'; // 显示燃气模型分析
    } else if (route.path ==="/drainage/decision") {
      showModel.value = 'drainage_model'; // 显示排水模型分析
    } else if (route.path ==="/heating/decision") {
      showModel.value = 'heating_model'; // 显示供热模型分析
    }
  },
  {
    deep: true,
  }
);

const leftTopTools = ref([
    {
        id: "8",
        icon: new URL(
            `./svg/fullScreen.svg`,
            import.meta.url
        ).href,
        label: "全屏",
    }
]);

const topTools = computed(() => {
  return [
      {
          id: "9",
          icon: new URL(
              `./svg/bottomMap.svg`,
              import.meta.url
          ).href,
          label: "地图切换",
          component: BottomMap,
      },
    {
      id: "1",
      icon: new URL(
          `./svg/search.svg`,
          import.meta.url
      ).href,
      label: "搜索",
      component: Search,
    },
    {
      id: "2",
      icon: new URL(
          `./svg/layerTree.svg`,
          import.meta.url
      ).href,
      label: "图层",
      component: LayerTree,
    },
    {
      id: "3",
      icon: new URL(
           `./svg/eyesEarth.svg`,
           import.meta.url
      ).href,
      label: "透视地图",
      component: EyesEarth,
    },
   /* {
      id: "3",
      // icon: showPane.value ? "show" : "hide",
      icon: new URL(
          `./svg/show.svg`,
          import.meta.url
      ).href,
      label: showPane.value ? "收起" : "展开",
    },*/
  ];
});
const bottomTools = ref([
  {
    id: "4",
    icon: new URL(
        `./svg/zoomOut.svg`,
        import.meta.url
    ).href,
    label: "放大",
  },
  {
    id: "5",
    icon: new URL(
        `./svg/zoomIn.svg`,
        import.meta.url
    ).href,
    label: "缩小",
  },
  {
    id: "6",
    icon: new URL(
        `./svg/reset.svg`,
        import.meta.url
    ).href,
    label: "复位",
  },
  {
    id: "7",
    icon: new URL(
        `./svg/legend.svg`,
        import.meta.url
    ).href,
    label: "图例",
  },
]);

const resetPostion = () => {
    // 根据地图模式设置不同的相机高度
    const height = state.mapType === 'map3d' ? 7000 : 12000;
    const pitch = state.mapType === 'map3d' ? -45 : -90;
    const lat = state.mapType === 'map3d' ? 35.221 : 35.221 + 0.07;

    mapStates.earth.camera.flyTo({
        lon: 115.097,
        lat: lat,
        height: height,
        orientation: {
            heading: 0,
            pitch: pitch,
            roll: 0,
        },
    });
};

const handleLeftSelectTool = (tool) => {
    if (tool.id === leftActiveId.value){
        leftActiveId.value = "";
        document.exitFullscreen();
    } else {
        leftActiveId.value = tool.id;
        document.documentElement.requestFullscreen();
    }
  /*if (tool.id === "8") {
      leftActiveId.value = tool.id;
      if (document.fullscreenElement) {
          document.exitFullscreen();
      } else {
          document.documentElement.requestFullscreen();
      }
  }*/
};
const handleTopSelectTool = (tool) => {
    if (tool.id === topActiveId.value){
        // 如果点击的工具已经是激活状态，则取消激活
        bus.emit("activeEyesEarth",  false);
        topActiveId.value = "";
        mapStates.viewer.scene.globe.translucency.enabled = false;
        mapStates.viewer.scene.globe.translucency.frontFaceAlpha = 1.0;
        mapStates.viewer.scene.globe.translucency.backFaceAlpha = 1.0;
        const osgbLayers = mapStates.viewer.scene.layers._layers.filter(layer => ["倾斜摄影","桥梁"].includes(layer.name));
        osgbLayers.forEach(layer => {
            layer.transparent = 100; // 恢复透明度
        })
    } else {
        //  如果点击的工具不是激活状态，则激活它
        topActiveId.value = tool.id;
        if (tool.id === "3") {
            bus.emit("resetEyesEarth") // 激活透视地图
            bus.emit("activeEyesEarth",  true); // 开启透视地图状态
        } else {
            // 取消透视地图
            bus.emit("activeEyesEarth",  false); // 关闭透视地图状态
            mapStates.viewer.scene.globe.translucency.enabled = false;
            mapStates.viewer.scene.globe.translucency.frontFaceAlpha = 1.0;
            mapStates.viewer.scene.globe.translucency.backFaceAlpha = 1.0;
            const osgbLayers = mapStates.viewer.scene.layers._layers.filter(layer => ["倾斜摄影","桥梁"].includes(layer.name));
            osgbLayers.forEach(layer => {
                layer.transparent = 100;
            })
        }
    }
  /*  switch (tool.id) {
        // 收起
        case "3":
            showPane.value = !showPane.value;
            bus.emit("togglePane", showPane.value);
            break;
    }*/
};
const handleBottomSelectTool = (tool) => {
  if (tool.id === "7") {
      bottomActiveId.value = tool.id;
  }
  switch (tool.id) {
    // 放大
    case "4":
        mapStates.earth.camera.onMapZoomIn();
      break;
    // 缩小
    case "5":
        mapStates.earth.camera.onMapZoomOut();
      break;
    // 复位
    case "6":
        resetPostion();
        bus.emit("resetGisPopup");
      break;
    // 图例
    case "7":
      showLegend.value = !showLegend.value;
      break;
  }
};
</script>

<style lang="scss" scoped>
.component-tools {
  position: absolute;
  top: 154px;
  bottom: 60px;
  left: 530px;
  right: 530px;
  display: flex;
  justify-content: space-between;
  z-index: 999;
  cursor: pointer;
  pointer-events: none;
  .tool-right,
  .tool-center,
  .tool-left {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    pointer-events: auto;
  }
  .tool-item-btn {
    pointer-events: all;
    width: 32px;
    height: 32px;
    background: rgba(13, 37, 82, 0.8);
    border: 1px solid #182d54;
    border-radius: 4px;
    margin-bottom: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    img {
      width: 32px;
      height: 32px;
    }
    &.active {
      background: rgba(26, 142, 231, 0.8);
      border: 1px solid rgba(26, 142, 231, 1);
    }
  }
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background: #263e58; /* 滑块颜色 */
  }
  &::-webkit-scrollbar-track {
    background: #162744; /* 轨道颜色 */
  }
}

.model-layout {
  position: absolute;
  top: 154px;
  bottom: 60px;
  left: 50px;
  right: 50px;
  z-index: 9999 !important;
}
</style>

<style lang="scss">
.el-popover.el-popper.gis-tool-popover {
  background: transparent;
  border: none;
  padding: 0;
  border-radius: 0;
  z-index: 998 !important;
}
</style>
