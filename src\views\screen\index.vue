<template>
  <div class="screen-wrapper">
    <div class="screen-container">
      <!-- 地图区域（全屏底层） -->
      <div class="map-control">
          <Tools/>
      </div>
      <div class="map-background">
        <MapScreen />
      </div>

      <!-- 左侧蒙版 -->
      <div class="screen-mask left-mask"></div>

      <!-- 右侧蒙版 -->
      <div class="screen-mask right-mask"></div>

      <!-- 左侧装饰边框 -->
      <div class="side-decoration left-side"></div>

      <!-- 右侧装饰边框（镜像翻转） -->
      <div class="side-decoration right-side"></div>

      <!-- 底部导航,独立于其他内容,充满屏幕宽度 -->
      <div class="footer-nav-container" v-show="!shouldHideFooter">
        <ScreenFooter :active-tab="primaryTab" />
      </div>

      <div class="screen-content">
        <!-- 顶部信息栏 -->
        <Header :showUser="true" />

        <!-- 二级导航栏 -->
        <div class="screen-sub-nav-container" v-if="primaryTab !== 'bridge'">
          <ScreenSubNav :active-tab="primaryTab" :active-sub-tab="secondaryTab" />
        </div>
        <!-- 占位空间,保持布局一致 -->
        <div class="screen-sub-nav-container" v-else></div>

        <!-- 主体内容区 -->
        <div class="main-content">
          <!-- 左侧面板 -->
          <div class="left-panel flex flex-col space-y-2">
            <component :is="getComponentByTabs(primaryTab, secondaryTab, 'LeftTopPanel')" class="panel-height-1"
              :class="[primaryTab === 'bridge' ? (bridgeStore.isDetailMode ? `${primaryTab}-detail-left-top-panel` : `${primaryTab}-left-top-panel`) : `${primaryTab}-${secondaryTab}-left-top-panel`]"
              :bridge-id="bridgeStore.selectedBridgeId"
              :bridge-name="bridgeStore.selectedBridgeName" />
            <component :is="getComponentByTabs(primaryTab, secondaryTab, 'LeftMiddlePanel')" class="panel-height-1"
              :class="[primaryTab === 'bridge' ? (bridgeStore.isDetailMode ? `${primaryTab}-detail-left-middle-panel` : `${primaryTab}-left-middle-panel`) : `${primaryTab}-${secondaryTab}-left-middle-panel`]"
              :bridge-id="bridgeStore.selectedBridgeId"
              :bridge-name="bridgeStore.selectedBridgeName" />
            <component :is="getComponentByTabs(primaryTab, secondaryTab, 'LeftBottomPanel')" class="panel-height-1"
              :class="[primaryTab === 'bridge' ? (bridgeStore.isDetailMode ? `${primaryTab}-detail-left-bottom-panel` : `${primaryTab}-left-bottom-panel`) : `${primaryTab}-${secondaryTab}-left-bottom-panel`]"
              :bridge-id="bridgeStore.selectedBridgeId"
              :bridge-name="bridgeStore.selectedBridgeName" />
            <div class="panel-bottom-space"></div>
          </div>

          <!-- 中间空白区域,地图已移至底层 -->
          <div class="center-panel flex flex-col">
            <div class="flex-1"></div>
          </div>

          <!-- 右侧面板 -->
          <div class="right-panel flex flex-col space-y-2">
            <!-- 桥梁详情模式下的返回按钮 -->
            <div v-if="showBridgeReturnButton" class="bridge-info-header">
              <div class="bridge-name-container" @click="handleBridgeReturn">
                <div class="back-icon">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M10 12L6 8L10 4" stroke="#00F2F1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <div class="bridge-name">{{ bridgeStore.selectedBridgeName }}</div>
              </div>
            </div>

            <component :is="getComponentByTabs(primaryTab, secondaryTab, 'RightTopPanel')" class="panel-height-1"
              :class="[primaryTab === 'bridge' ? (bridgeStore.isDetailMode ? `${primaryTab}-detail-right-top-panel` : `${primaryTab}-right-top-panel`) : `${primaryTab}-${secondaryTab}-right-top-panel`]"
              :bridge-id="bridgeStore.selectedBridgeId"
              :bridge-name="bridgeStore.selectedBridgeName" />
            <component :is="getComponentByTabs(primaryTab, secondaryTab, 'RightMiddlePanel')" class="panel-height-1"
              :class="[primaryTab === 'bridge' ? (bridgeStore.isDetailMode ? `${primaryTab}-detail-right-middle-panel` : `${primaryTab}-right-middle-panel`) : `${primaryTab}-${secondaryTab}-right-middle-panel`]"
              :bridge-id="bridgeStore.selectedBridgeId"
              :bridge-name="bridgeStore.selectedBridgeName" />
            <component :is="getComponentByTabs(primaryTab, secondaryTab, 'RightBottomPanel')" class="panel-height-1"
              :class="[primaryTab === 'bridge' ? (bridgeStore.isDetailMode ? `${primaryTab}-detail-right-bottom-panel` : `${primaryTab}-right-bottom-panel`) : `${primaryTab}-${secondaryTab}-right-bottom-panel`]"
              :bridge-id="bridgeStore.selectedBridgeId"
              :bridge-name="bridgeStore.selectedBridgeName" />
            <div class="panel-bottom-space"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 全局报警消息弹窗 -->
    <GlobalAlarmModal mode="screen" />

    <!-- 报警通知按钮 -->
    <AlarmNotificationButton mode="screen" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useBridgeStore } from '@/stores/bridge'

// 导入边框背景图
import sideBgImage from '@/assets/images/screen/screen_side_bg.png'

// 导入 Header 组件
import Header from '@/components/common/Header.vue'

// 导入中心组件
import CenterMapPanel from '@/components/screen/panels/CenterMapPanel.vue'
// 导入导航组件
import ScreenFooter from '@/components/screen/panels/ScreenFooter.vue'
import ScreenSubNav from '@/components/screen/panels/ScreenSubNav.vue'
//地图组件
import { mapStates } from "@/components/GisMap/mapStates.js";
import MapScreen from "@/components/GisMap/components/MapScreen/index.vue";
import Tools from "@/components/GisMap/Tools/index.vue";
// 全局报警消息弹窗组件
import GlobalAlarmModal from '@/components/GlobalAlarmModal.vue'
// 报警通知按钮组件
import AlarmNotificationButton from '@/components/AlarmNotificationButton.vue'

// 导入各专项的面板组件
// 燃气专项 - 综合态势
import GasOverviewLeftTopPanel from '@/components/screen/panels/gas/overview/LeftTopPanel.vue'
import GasOverviewLeftMiddlePanel from '@/components/screen/panels/gas/overview/LeftMiddlePanel.vue'
import GasOverviewLeftBottomPanel from '@/components/screen/panels/gas/overview/LeftBottomPanel.vue'
import GasOverviewRightTopPanel from '@/components/screen/panels/gas/overview/RightTopPanel.vue'
import GasOverviewRightMiddlePanel from '@/components/screen/panels/gas/overview/RightMiddlePanel.vue'
import GasOverviewRightBottomPanel from '@/components/screen/panels/gas/overview/RightBottomPanel.vue'

// 燃气专项 - 管网风险
import GasNetworkRiskLeftTopPanel from '@/components/screen/panels/gas/networkRisk/LeftTopPanel.vue'
import GasNetworkRiskLeftMiddlePanel from '@/components/screen/panels/gas/networkRisk/LeftMiddlePanel.vue'
import GasNetworkRiskLeftBottomPanel from '@/components/screen/panels/gas/networkRisk/LeftBottomPanel.vue'
import GasNetworkRiskRightTopPanel from '@/components/screen/panels/gas/networkRisk/RightTopPanel.vue'
import GasNetworkRiskRightMiddlePanel from '@/components/screen/panels/gas/networkRisk/RightMiddlePanel.vue'

// 燃气专项 - 监测报警
import GasMonitoringLeftTopPanel from '@/components/screen/panels/gas/monitoring/LeftTopPanel.vue'
import GasMonitoringLeftMiddlePanel from '@/components/screen/panels/gas/monitoring/LeftMiddlePanel.vue'
import GasMonitoringLeftBottomPanel from '@/components/screen/panels/gas/monitoring/LeftBottomPanel.vue'
import GasMonitoringRightTopPanel from '@/components/screen/panels/gas/monitoring/RightTopPanel.vue'
import GasMonitoringRightMiddlePanel from '@/components/screen/panels/gas/monitoring/RightMiddlePanel.vue'
import GasMonitoringRightBottomPanel from '@/components/screen/panels/gas/monitoring/RightBottomPanel.vue'

// 综合专项 - 综合态势总览
import ComprehensiveOverviewLeftTopPanel from '@/components/screen/panels/comprehensive/overview/LeftTopPanel.vue'
import ComprehensiveOverviewLeftMiddlePanel from '@/components/screen/panels/comprehensive/overview/LeftMiddlePanel.vue'
import ComprehensiveOverviewLeftBottomPanel from '@/components/screen/panels/comprehensive/overview/LeftBottomPanel.vue'
import ComprehensiveOverviewRightTopPanel from '@/components/screen/panels/comprehensive/overview/RightTopPanel.vue'
import ComprehensiveOverviewRightMiddlePanel from '@/components/screen/panels/comprehensive/overview/RightMiddlePanel.vue'
import ComprehensiveOverviewRightBottomPanel from '@/components/screen/panels/comprehensive/overview/RightBottomPanel.vue'

// 综合专项 - 综合风险隐患
import ComprehensiveRiskLeftTopPanel from '@/components/screen/panels/comprehensive/risk/LeftTopPanel.vue'
import ComprehensiveRiskLeftMiddlePanel from '@/components/screen/panels/comprehensive/risk/LeftMiddlePanel.vue'
import ComprehensiveRiskLeftBottomPanel from '@/components/screen/panels/comprehensive/risk/LeftBottomPanel.vue'
import ComprehensiveRiskRightTopPanel from '@/components/screen/panels/comprehensive/risk/RightTopPanel.vue'
import ComprehensiveRiskRightMiddlePanel from '@/components/screen/panels/comprehensive/risk/RightMiddlePanel.vue'
import ComprehensiveRiskRightBottomPanel from '@/components/screen/panels/comprehensive/risk/RightBottomPanel.vue'

// 综合专项 - 综合运行监测
import ComprehensiveMonitoringLeftTopPanel from '@/components/screen/panels/comprehensive/monitoring/LeftTopPanel.vue'
import ComprehensiveMonitoringLeftMiddlePanel from '@/components/screen/panels/comprehensive/monitoring/LeftMiddlePanel.vue'
import ComprehensiveMonitoringLeftBottomPanel from '@/components/screen/panels/comprehensive/monitoring/LeftBottomPanel.vue'
import ComprehensiveMonitoringRightTopPanel from '@/components/screen/panels/comprehensive/monitoring/RightTopPanel.vue'
import ComprehensiveMonitoringRightMiddlePanel from '@/components/screen/panels/comprehensive/monitoring/RightMiddlePanel.vue'
import ComprehensiveMonitoringRightBottomPanel from '@/components/screen/panels/comprehensive/monitoring/RightBottomPanel.vue'

// 综合专项 - 协同联动处置
import ComprehensiveCoordinationLeftTopPanel from '@/components/screen/panels/comprehensive/coordination/LeftTopPanel.vue'
import ComprehensiveCoordinationLeftMiddlePanel from '@/components/screen/panels/comprehensive/coordination/LeftMiddlePanel.vue'
import ComprehensiveCoordinationLeftBottomPanel from '@/components/screen/panels/comprehensive/coordination/LeftBottomPanel.vue'
import ComprehensiveCoordinationRightTopPanel from '@/components/screen/panels/comprehensive/coordination/RightTopPanel.vue'
import ComprehensiveCoordinationRightMiddlePanel from '@/components/screen/panels/comprehensive/coordination/RightMiddlePanel.vue'
// import ComprehensiveCoordinationRightBottomPanel from '@/components/screen/panels/comprehensive/coordination/RightBottomPanel.vue'

// 综合专项 - 应急辅助决策
import ComprehensiveEmergencyLeftTopPanel from '@/components/screen/panels/comprehensive/emergency/LeftTopPanel.vue'
import ComprehensiveEmergencyLeftMiddlePanel from '@/components/screen/panels/comprehensive/emergency/LeftMiddlePanel.vue'
import ComprehensiveEmergencyLeftBottomPanel from '@/components/screen/panels/comprehensive/emergency/LeftBottomPanel.vue'
import ComprehensiveEmergencyRightTopPanel from '@/components/screen/panels/comprehensive/emergency/RightTopPanel.vue'
import ComprehensiveEmergencyRightMiddlePanel from '@/components/screen/panels/comprehensive/emergency/RightMiddlePanel.vue'
import ComprehensiveEmergencyRightBottomPanel from '@/components/screen/panels/comprehensive/emergency/RightBottomPanel.vue'

// 排水专项 - 综合态势
import DrainageOverviewLeftTopPanel from '@/components/screen/panels/drainage/overview/LeftTopPanel.vue'
import DrainageOverviewLeftMiddlePanel from '@/components/screen/panels/drainage/overview/LeftMiddlePanel.vue'
import DrainageOverviewLeftBottomPanel from '@/components/screen/panels/drainage/overview/LeftBottomPanel.vue'
import DrainageOverviewRightTopPanel from '@/components/screen/panels/drainage/overview/RightTopPanel.vue'
import DrainageOverviewRightMiddlePanel from '@/components/screen/panels/drainage/overview/RightMiddlePanel.vue'
import DrainageOverviewRightBottomPanel from '@/components/screen/panels/drainage/overview/RightBottomPanel.vue'

// 排水专项 - 风险隐患
import DrainageRiskLeftTopPanel from '@/components/screen/panels/drainage/risk/LeftTopPanel.vue'
import DrainageRiskLeftMiddlePanel from '@/components/screen/panels/drainage/risk/LeftMiddlePanel.vue'
import DrainageRiskLeftBottomPanel from '@/components/screen/panels/drainage/risk/LeftBottomPanel.vue'
import DrainageRiskRightTopPanel from '@/components/screen/panels/drainage/risk/RightTopPanel.vue'
import DrainageRiskRightMiddlePanel from '@/components/screen/panels/drainage/risk/RightMiddlePanel.vue'
import DrainageRiskRightBottomPanel from '@/components/screen/panels/drainage/risk/RightBottomPanel.vue'

// 排水专项 - 易涝点风险
import DrainageFloodingRiskLeftTopPanel from '@/components/screen/panels/drainage/floodingRisk/LeftTopPanel.vue'
import DrainageFloodingRiskLeftMiddlePanel from '@/components/screen/panels/drainage/floodingRisk/LeftMiddlePanel.vue'

// 排水专项 - 监测报警
import DrainageMonitoringLeftTopPanel from '@/components/screen/panels/drainage/monitoring/LeftTopPanel.vue'
import DrainageMonitoringLeftMiddlePanel from '@/components/screen/panels/drainage/monitoring/LeftMiddlePanel.vue'
import DrainageMonitoringLeftBottomPanel from '@/components/screen/panels/drainage/monitoring/LeftBottomPanel.vue'
import DrainageMonitoringRightTopPanel from '@/components/screen/panels/drainage/monitoring/RightTopPanel.vue'
import DrainageMonitoringRightMiddlePanel from '@/components/screen/panels/drainage/monitoring/RightMiddlePanel.vue'
import DrainageMonitoringRightBottomPanel from '@/components/screen/panels/drainage/monitoring/RightBottomPanel.vue'

// 供热专项 - 综合态势
import HeatingOverviewLeftTopPanel from '@/components/screen/panels/heating/overview/LeftTopPanel.vue'
import HeatingOverviewLeftMiddlePanel from '@/components/screen/panels/heating/overview/LeftMiddlePanel.vue'
import HeatingOverviewLeftBottomPanel from '@/components/screen/panels/heating/overview/LeftBottomPanel.vue'
import HeatingOverviewRightTopPanel from '@/components/screen/panels/heating/overview/RightTopPanel.vue'
import HeatingOverviewRightMiddlePanel from '@/components/screen/panels/heating/overview/RightMiddlePanel.vue'
import HeatingOverviewRightBottomPanel from '@/components/screen/panels/heating/overview/RightBottomPanel.vue'

// 供热专项 - 供热风险隐患
import HeatingRiskLeftTopPanel from '@/components/screen/panels/heating/risk/LeftTopPanel.vue'
import HeatingRiskLeftMiddlePanel from '@/components/screen/panels/heating/risk/LeftMiddlePanel.vue'
import HeatingRiskLeftBottomPanel from '@/components/screen/panels/heating/risk/LeftBottomPanel.vue'
import HeatingRiskRightTopPanel from '@/components/screen/panels/heating/risk/RightTopPanel.vue'
import HeatingRiskRightMiddlePanel from '@/components/screen/panels/heating/risk/RightMiddlePanel.vue'
import HeatingRiskRightBottomPanel from '@/components/screen/panels/heating/risk/RightBottomPanel.vue'

// 供热专项 - 供热监测报警
import HeatingMonitoringLeftTopPanel from '@/components/screen/panels/heating/monitoring/LeftTopPanel.vue'
import HeatingMonitoringLeftMiddlePanel from '@/components/screen/panels/heating/monitoring/LeftMiddlePanel.vue'
import HeatingMonitoringLeftBottomPanel from '@/components/screen/panels/heating/monitoring/LeftBottomPanel.vue'
import HeatingMonitoringRightTopPanel from '@/components/screen/panels/heating/monitoring/RightTopPanel.vue'
import HeatingMonitoringRightMiddlePanel from '@/components/screen/panels/heating/monitoring/RightMiddlePanel.vue'
import HeatingMonitoringRightBottomPanel from '@/components/screen/panels/heating/monitoring/RightBottomPanel.vue'

// 桥梁专项
import BridgeLeftTopPanel from '@/components/screen/panels/bridge/LeftTopPanel.vue'
import BridgeLeftMiddlePanel from '@/components/screen/panels/bridge/LeftMiddlePanel.vue'
import BridgeLeftBottomPanel from '@/components/screen/panels/bridge/LeftBottomPanel.vue'
import BridgeRightTopPanel from '@/components/screen/panels/bridge/RightTopPanel.vue'
import BridgeRightMiddlePanel from '@/components/screen/panels/bridge/RightMiddlePanel.vue'
import BridgeRightBottomPanel from '@/components/screen/panels/bridge/RightBottomPanel.vue'

// 桥梁详情专项
import BridgeDetailLeftTopPanel from '@/components/screen/panels/bridge/detail/LeftTopPanel.vue'
import BridgeDetailLeftMiddlePanel from '@/components/screen/panels/bridge/detail/LeftMiddlePanel.vue'
import BridgeDetailLeftBottomPanel from '@/components/screen/panels/bridge/detail/LeftBottomPanel.vue'
import BridgeDetailRightTopPanel from '@/components/screen/panels/bridge/detail/RightTopPanel.vue'
import BridgeDetailRightMiddlePanel from '@/components/screen/panels/bridge/detail/RightMiddlePanel.vue'
import BridgeDetailRightBottomPanel from '@/components/screen/panels/bridge/detail/RightBottomPanel.vue'

// 获取当前路由
const route = useRoute()

// 桥梁状态管理
const bridgeStore = useBridgeStore()

// 当前时间日期
const currentTime = ref('')
const currentDate = ref('')

const hideMap = computed(() => {
  return route.path === "/screen/waterSupplyProcess";
});

// 从路由参数中获取当前活动的专项和子专项
const primaryTab = computed(() => {
  console.log('Route params:', route.params);
  console.log('Route path:', route.path);
  // 对于桥梁专项的特殊处理
  if (route.path === '/bridge') {
    return 'bridge';
  }
  return route.params.primaryTab || 'comprehensive';
});
const secondaryTab = computed(() => route.params.secondaryTab || 'overview');

// 更新时间
const updateDateTime = () => {
  const now = new Date()
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  currentTime.value = `${hours}:${minutes}:${seconds}`

  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const weekdays = ['日', '一', '二', '三', '四', '五', '六']
  currentDate.value = `${year}.${month}.${day} 星期${weekdays[now.getDay()]}`
}

// 获取专项标题
const getActiveTabTitle = () => {
  const tabTitles = {
    'gas': '燃气',
    'drainage': '排水',
    'comprehensive': '综合',
    'heating': '供热',
    'bridge': '桥梁'
  }
  return tabTitles[primaryTab.value] || '综合'
}

// 根据当前专项和二级专项获取对应的面板组件
const getComponentByTabs = (tab, subTab, componentName) => {
  console.log(`Getting component for: ${tab}/${subTab}/${componentName}`);

  // 桥梁专项特殊处理
  if (tab === 'bridge') {
    console.log('Using bridge components');

    // 如果是详情模式，返回详情组件
    if (bridgeStore.isDetailMode) {
      return {
        'LeftTopPanel': BridgeDetailLeftTopPanel,
        'LeftMiddlePanel': BridgeDetailLeftMiddlePanel,
        'LeftBottomPanel': BridgeDetailLeftBottomPanel,
        'RightTopPanel': BridgeDetailRightTopPanel,
        'RightMiddlePanel': BridgeDetailRightMiddlePanel,
        'RightBottomPanel': BridgeDetailRightBottomPanel
      }[componentName];
    }

    // 否则返回普通桥梁组件
    return {
      'LeftTopPanel': BridgeLeftTopPanel,
      'LeftMiddlePanel': BridgeLeftMiddlePanel,
      'LeftBottomPanel': BridgeLeftBottomPanel,
      'RightTopPanel': BridgeRightTopPanel,
      'RightMiddlePanel': BridgeRightMiddlePanel,
      'RightBottomPanel': BridgeRightBottomPanel
    }[componentName];
  }

  // 组件映射
  const componentMap = {
    'comprehensive': {
      'overview': {
        'LeftTopPanel': ComprehensiveOverviewLeftTopPanel,
        'LeftMiddlePanel': ComprehensiveOverviewLeftMiddlePanel,
        'LeftBottomPanel': ComprehensiveOverviewLeftBottomPanel,
        'RightTopPanel': ComprehensiveOverviewRightTopPanel,
        'RightMiddlePanel': ComprehensiveOverviewRightMiddlePanel,
        'RightBottomPanel': ComprehensiveOverviewRightBottomPanel
      },
      'risk': {
        'LeftTopPanel': ComprehensiveRiskLeftTopPanel,
        'LeftMiddlePanel': ComprehensiveRiskLeftMiddlePanel,
        'LeftBottomPanel': ComprehensiveRiskLeftBottomPanel,
        'RightTopPanel': ComprehensiveRiskRightTopPanel,
        'RightMiddlePanel': ComprehensiveRiskRightMiddlePanel,
        'RightBottomPanel': ComprehensiveRiskRightBottomPanel
      },
      'monitoring': {
        'LeftTopPanel': ComprehensiveMonitoringLeftTopPanel,
        'LeftMiddlePanel': ComprehensiveMonitoringLeftMiddlePanel,
        'LeftBottomPanel': ComprehensiveMonitoringLeftBottomPanel,
        'RightTopPanel': ComprehensiveMonitoringRightTopPanel,
        'RightMiddlePanel': ComprehensiveMonitoringRightMiddlePanel,
        'RightBottomPanel': ComprehensiveMonitoringRightBottomPanel
      },
      'coordination': {
        'LeftTopPanel': ComprehensiveCoordinationLeftTopPanel,
        'LeftMiddlePanel': ComprehensiveCoordinationLeftMiddlePanel,
        'LeftBottomPanel': ComprehensiveCoordinationLeftBottomPanel,
        'RightTopPanel': ComprehensiveCoordinationRightTopPanel,
        'RightMiddlePanel': ComprehensiveCoordinationRightMiddlePanel,
        // 'RightBottomPanel': ComprehensiveCoordinationRightBottomPanel
      },
      'emergency': {
        'LeftTopPanel': ComprehensiveEmergencyLeftTopPanel,
        'LeftMiddlePanel': ComprehensiveEmergencyLeftMiddlePanel,
        'LeftBottomPanel': ComprehensiveEmergencyLeftBottomPanel,
        'RightTopPanel': ComprehensiveEmergencyRightTopPanel,
        'RightMiddlePanel': ComprehensiveEmergencyRightMiddlePanel,
        'RightBottomPanel': ComprehensiveEmergencyRightBottomPanel
      }
      // 其他综合专项的二级菜单组件可以在这里添加
    },
    'gas': {
      'overview': {
        'LeftTopPanel': GasOverviewLeftTopPanel,
        'LeftMiddlePanel': GasOverviewLeftMiddlePanel,
        'LeftBottomPanel': GasOverviewLeftBottomPanel,
        'RightTopPanel': GasOverviewRightTopPanel,
        'RightMiddlePanel': GasOverviewRightMiddlePanel,
        'RightBottomPanel': GasOverviewRightBottomPanel
      },
      'network-risk': {
        'LeftTopPanel': GasNetworkRiskLeftTopPanel,
        'LeftMiddlePanel': GasNetworkRiskLeftMiddlePanel,
        'LeftBottomPanel': GasNetworkRiskLeftBottomPanel,
        'RightTopPanel': GasNetworkRiskRightTopPanel,
        'RightMiddlePanel': GasNetworkRiskRightMiddlePanel,
      },
      'monitoring': {
        'LeftTopPanel': GasMonitoringLeftTopPanel,
        'LeftMiddlePanel': GasMonitoringLeftMiddlePanel,
        'LeftBottomPanel': GasMonitoringLeftBottomPanel,
        'RightTopPanel': GasMonitoringRightTopPanel,
        'RightMiddlePanel': GasMonitoringRightMiddlePanel,
        'RightBottomPanel': GasMonitoringRightBottomPanel
      },
      'decision-screen': {}
      // 其他燃气专项的二级菜单组件可以在这里添加
    },
    'drainage': {
      'overview': {
        'LeftTopPanel': DrainageOverviewLeftTopPanel,
        'LeftMiddlePanel': DrainageOverviewLeftMiddlePanel,
        'LeftBottomPanel': DrainageOverviewLeftBottomPanel,
        'RightTopPanel': DrainageOverviewRightTopPanel,
        'RightMiddlePanel': DrainageOverviewRightMiddlePanel,
        'RightBottomPanel': DrainageOverviewRightBottomPanel
      },
      'risk': {
        'LeftTopPanel': DrainageRiskLeftTopPanel,
        'LeftMiddlePanel': DrainageRiskLeftMiddlePanel,
        'LeftBottomPanel': DrainageRiskLeftBottomPanel,
        'RightTopPanel': DrainageRiskRightTopPanel,
        'RightMiddlePanel': DrainageRiskRightMiddlePanel,
        'RightBottomPanel': DrainageRiskRightBottomPanel
      },
      'flooding-risk': {
        'LeftTopPanel': DrainageFloodingRiskLeftTopPanel,
        'LeftMiddlePanel': DrainageFloodingRiskLeftMiddlePanel,
      },
      'monitoring': {
        'LeftTopPanel': DrainageMonitoringLeftTopPanel,
        'LeftMiddlePanel': DrainageMonitoringLeftMiddlePanel,
        'LeftBottomPanel': DrainageMonitoringLeftBottomPanel,
        'RightTopPanel': DrainageMonitoringRightTopPanel,
        'RightMiddlePanel': DrainageMonitoringRightMiddlePanel,
        'RightBottomPanel': DrainageMonitoringRightBottomPanel
      },
      'decision': {}
      // 其他排水专项的二级菜单组件可以在这里添加
    },
    'heating': {
      'overview': {
        'LeftTopPanel': HeatingOverviewLeftTopPanel,
        'LeftMiddlePanel': HeatingOverviewLeftMiddlePanel,
        'LeftBottomPanel': HeatingOverviewLeftBottomPanel,
        'RightTopPanel': HeatingOverviewRightTopPanel,
        'RightMiddlePanel': HeatingOverviewRightMiddlePanel,
        'RightBottomPanel': HeatingOverviewRightBottomPanel
      },
      'risk': {
        'LeftTopPanel': HeatingRiskLeftTopPanel,
        'LeftMiddlePanel': HeatingRiskLeftMiddlePanel,
        'LeftBottomPanel': HeatingRiskLeftBottomPanel,
        'RightTopPanel': HeatingRiskRightTopPanel,
        'RightMiddlePanel': HeatingRiskRightMiddlePanel,
        'RightBottomPanel': HeatingRiskRightBottomPanel
      },
      'monitoring': {
        'LeftTopPanel': HeatingMonitoringLeftTopPanel,
        'LeftMiddlePanel': HeatingMonitoringLeftMiddlePanel,
        'LeftBottomPanel': HeatingMonitoringLeftBottomPanel,
        'RightTopPanel': HeatingMonitoringRightTopPanel,
        'RightMiddlePanel': HeatingMonitoringRightMiddlePanel,
        'RightBottomPanel': HeatingMonitoringRightBottomPanel
      },
      'decision': {}
      // 其他供热专项的二级菜单组件可以在这里添加
    }
  };

  // 如果没有对应专项的组件,使用综合专项的组件
  if (!componentMap[tab]) {
    tab = 'comprehensive';
  }

  // 如果没有对应二级专项的组件,使用综合态势的组件
  if (!componentMap[tab][subTab]) {
    subTab = 'overview';
  }

  try {
    return componentMap[tab][subTab][componentName];
  } catch (error) {
    // 如果找不到对应的组件,返回综合态势总览的对应组件
    console.error(`无法找到组件 ${tab}/${subTab}/${componentName}`, error);
    return componentMap['comprehensive']['overview'][componentName];
  }
}

// 监听屏幕大小变化
const handleResize = () => {
  // 屏幕大小变化时更新布局
  console.log('Window resized, updating layout');
};

let timeInterval = null

// 初始化
onMounted(() => {
  // 添加screen页面class
  document.body.classList.add('screen-page')

  // 启动时间更新
  updateDateTime()
  timeInterval = setInterval(updateDateTime, 1000)

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
  // 初始触发一次resize
  handleResize()
})

// 清理资源
onUnmounted(() => {
  // 移除screen页面class
  document.body.classList.remove('screen-page')

  if (timeInterval) {
    clearInterval(timeInterval)
    timeInterval = null
  }

  // 移除事件监听
  window.removeEventListener('resize', handleResize)
})

const resetPostion = () => {
    mapStates.earth.camera.flyTo({
        lon: 115.097,
        lat: 35.288,
        height: 8000,
        orientation: {
            heading: 0,
            pitch: -90, //-45
            roll: 0,
        },
    });
};

// 处理桥梁返回按钮点击事件
const handleBridgeReturn = () => {
  bridgeStore.exitDetailMode();
  resetPostion(); //重置位置
}

// 显示桥梁返回按钮
const showBridgeReturnButton = computed(() => {
  return primaryTab.value === 'bridge' && bridgeStore.isDetailMode
})

// 是否隐藏底部菜单
const shouldHideFooter = computed(() => {
  return primaryTab.value === 'bridge' && bridgeStore.isDetailMode
})

// 全局暴露进入桥梁详情模式的方法，供其他组件调用
window.enterBridgeDetailMode = (bridgeId, bridgeName) => {
  bridgeStore.enterDetailMode(bridgeId, bridgeName)
}
</script>

<style scoped>
/* 使用 CSS 变量定义可复用的尺寸 */
:root {
  --panel-height-base: 310px;
  /* 基础面板高度 */
  --panel-height-s: 280px;
  /* 小面板高度 */
  --panel-height-m: 310px;
  /* 中面板高度 */
  --panel-height-l: 370px;
  /* 大面板高度 */
}

.screen-wrapper {
  width: 100vw;
  height: 100vh;
  background-color: #0d2341;
  background-image:
    radial-gradient(circle at center, rgba(26, 59, 109, 0.3) 0%, rgba(13, 35, 65, 0.3) 100%),
    linear-gradient(to bottom, #0d2341, #1a3b6d);
  overflow: hidden;
}

.screen-container {
  width: 100vw;
  /* 100%视口宽度 */
  height: 100vh;
  /* 100%视口高度 */
  position: relative;
  overflow: hidden;
}

/* 地图背景样式 */
.map-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  /* 提高地图层级,但仍保持在内容下方 */
  cursor: pointer;
  pointer-events: auto;
  /* 确保地图可以接收交互事件 */
}

/* 底部导航容器,独立于其他内容 */
.footer-nav-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3.7rem;
  /* 使用rem单位替代固定px值 */
  z-index: 20;
  /* 提高层级,确保在最上层 */
  pointer-events: auto;
  /* 确保底部导航可点击 */
}

.screen-content {
  position: relative;
  z-index: 10;
  /* 确保内容在地图上层 */
  padding: 0;
  /* 移除所有内边距,由Header组件控制自己的内边距 */
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(to right, rgba(0, 242, 241, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(0, 242, 241, 0.05) 1px, transparent 1px);
  background-size: 2.5rem 2.5rem;
  /* 使用rem替代固定的40px */
  background-color: transparent;
  /* 背景透明,可以看到底层地图 */
  display: flex;
  flex-direction: column;
  pointer-events: none;
  /* 允许事件穿透到底层地图 */
}

/* 只有左右面板和顶部组件接收点击事件 */
.screen-content>header,
.left-panel,
.right-panel,
.screen-sub-nav-container {
  pointer-events: auto;
  /* 恢复这些元素的点击事件 */
}

/* 中央区域保持穿透 */
.main-content {
  pointer-events: none;
  display: grid;
  grid-template-columns: 29.5rem minmax(0, 1fr) 29.5rem;
  /* 使用rem替代固定的472px */
  gap: 0.625rem;
  /* 使用rem替代固定的10px */
  flex: 1;
  min-height: 0;
  /* 修复flex子元素不收缩的问题 */
  overflow: hidden;
  padding: 0 2.5rem;
  /* 使用rem替代固定的40px */
  margin-top: -1.5625rem;
  /* 使用rem替代固定的-25px */
}

.center-panel {
  height: 100%;
  pointer-events: none;
}

.screen-sub-nav-container {
  height: 2.5rem;
  /* 使用rem替代固定的40px */
  margin: 0.625rem 1.875rem 0.3125rem 1.875rem;
  /* 使用rem替代固定px值 */
  width: 50%;
  position: absolute;
  top: 6.25rem;
  /* 使用rem替代固定的100px */
  left: 25%;
}

.left-panel,
.right-panel {
  min-height: 0;
  /* 确保flex子元素可以收缩 */
}

.panel-height-1 {
  flex: 1;
  min-height: 0;
  backdrop-filter: blur(3px);
  /* 添加模糊效果,提高面板背景与地图的对比度 */
  height: auto;
  max-height: none;
}

/* 面板底部空间,用于保持与底部的距离 */
.panel-bottom-space {
  height: 3.125rem;
  /* 使用rem替代固定的50px */
  flex-shrink: 0;
}

/* 媒体查询 - 适配不同比例的屏幕 */
@media (min-width: 1920px) and (max-width: 2560px) {

  /* 16:9 到 21:9 的屏幕 */
  .main-content {
    grid-template-columns: 29.5rem minmax(0, 1fr) 29.5rem;
    /* 使用rem */
  }

  .panel-bottom-space {
    height: 3.125rem;
    /* 使用rem */
  }

  /* 设置面板高度 */
  .panel-height-1 {
    /* 根据面板位置设置不同高度 */
    height: var(--panel-height, 17.5rem);
    /* 280px -> 17.5rem */
    max-height: var(--panel-height, 17.5rem);
  }

  /* 右侧面板高度设置 */
  .right-top-panel {
    height: 17.5rem !important;
    /* 280px -> 17.5rem */
    max-height: 17.5rem !important;
  }

  .right-middle-panel {
    height: 19.375rem !important;
    /* 310px -> 19.375rem */
    max-height: 19.375rem !important;
  }

  .right-bottom-panel {
    height: 21.25rem !important;
    /* 340px -> 21.25rem */
    max-height: 21.25rem !important;
  }

  /* 燃气专项-综合态势左侧面板高度 */
  .left-panel>.gas-overview-left-top-panel {
    --panel-height: 280px;
  }

  .left-panel>.gas-overview-left-middle-panel {
    --panel-height: 370px;
  }

  .left-panel>.gas-overview-left-bottom-panel {
    --panel-height: 280px;
  }

  /* 燃气专项-管网风险左侧面板高度 */
  .left-panel>.gas-network-risk-left-top-panel {
    --panel-height: 280px;
  }

  .left-panel>.gas-network-risk-left-middle-panel {
    --panel-height: 330px;
  }

  .left-panel>.gas-network-risk-left-bottom-panel {
    --panel-height: 320px;
  }

  /* 燃气专项-管网风险右侧面板高度 */
  .right-panel>.gas-network-risk-right-top-panel {
    height: 280px !important;
    max-height: 280px !important;
  }

  .right-panel>.gas-network-risk-right-middle-panel {
    height: 660px !important;
    max-height: 660px !important;
  }

  /* 燃气专项-监测报警左侧面板高度 */
  .left-panel>.gas-monitoring-left-top-panel {
    --panel-height: 280px;
  }

  .left-panel>.gas-monitoring-left-middle-panel {
    --panel-height: 330px;
  }

  .left-panel>.gas-monitoring-left-bottom-panel {
    --panel-height: 320px;
  }

  /* 燃气专项-监测报警右侧面板高度 */
  .right-panel>.gas-monitoring-right-top-panel {
    height: 280px !important;
    max-height: 280px !important;
  }

  .right-panel>.gas-monitoring-right-middle-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  .right-panel>.gas-monitoring-right-bottom-panel {
    height: 340px !important;
    max-height: 340px !important;
  }

  /* 综合专项面板高度 */
  .left-panel>.comprehensive-overview-left-top-panel,
  .left-panel>.comprehensive-risk-left-top-panel,
  .left-panel>.comprehensive-monitoring-left-top-panel {
    --panel-height: 280px;
  }

  .left-panel>.comprehensive-overview-left-middle-panel,
  .left-panel>.comprehensive-risk-left-middle-panel,
  .left-panel>.comprehensive-monitoring-left-middle-panel {
    --panel-height: 330px;
  }

  .left-panel>.comprehensive-overview-left-bottom-panel,
  .left-panel>.comprehensive-risk-left-bottom-panel,
  .left-panel>.comprehensive-monitoring-left-bottom-panel {
    --panel-height: 280px;
  }

  /* 排水专项面板高度 */
  .left-panel>.drainage-overview-left-top-panel,
  .left-panel>.drainage-risk-left-top-panel,
  .left-panel>.drainage-monitoring-left-top-panel,
  .right-panel>.drainage-monitoring-right-top-panel {
    --panel-height: 310px;
  }

  .left-panel>.drainage-flooding-risk-left-top-panel {
    --panel-height: 320px;
  }

  .left-panel>.drainage-overview-left-middle-panel,
  .left-panel>.drainage-risk-left-middle-panel,
  .left-panel>.drainage-monitoring-left-middle-panel,
  .right-panel>.drainage-monitoring-right-middle-panel {
    --panel-height: 310px;
  }

  .left-panel>.drainage-flooding-risk-left-middle-panel {
    --panel-height: 620px;
  }

  .left-panel>.drainage-overview-left-bottom-panel,
  .left-panel>.drainage-risk-left-bottom-panel,
  .left-panel>.drainage-flooding-risk-left-bottom-panel,
  .left-panel>.drainage-monitoring-left-bottom-panel,
  .right-panel>.drainage-monitoring-right-bottom-panel {
    --panel-height: 310px;
  }

  /* 供热专项 - 综合态势左侧面板高度 */
  .left-panel>.heating-overview-left-top-panel {
    --panel-height: 340px;
  }

  .left-panel>.heating-overview-left-middle-panel {
    --panel-height: 260px;
  }

  .left-panel>.heating-overview-left-bottom-panel {
    --panel-height: 320px;
  }

  /* 供热专项 - 综合态势右侧面板高度 */
  .right-panel>.heating-overview-right-top-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  .right-panel>.heating-overview-right-middle-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  .right-panel>.heating-overview-right-bottom-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  /* 供热专项 - 供热风险隐患左侧面板高度 */
  .left-panel>.heating-risk-left-top-panel {
    --panel-height: 280px;
  }

  .left-panel>.heating-risk-left-middle-panel {
    --panel-height: 320px;
  }

  .left-panel>.heating-risk-left-bottom-panel {
    --panel-height: 320px;
  }

  /* 供热专项 - 供热风险隐患右侧面板高度 */
  .right-panel>.heating-risk-right-top-panel {
    height: 340px !important;
    max-height: 340px !important;
  }

  .right-panel>.heating-risk-right-middle-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  .right-panel>.heating-risk-right-bottom-panel {
    height: 280px !important;
    max-height: 280px !important;
  }

  /* 供热专项 - 供热监测报警左侧面板高度 */
  .left-panel>.heating-monitoring-left-top-panel,
  .left-panel>.heating-monitoring-left-middle-panel,
  .left-panel>.heating-monitoring-left-bottom-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  /* 供热专项 - 供热监测报警右侧面板高度 */
  .right-panel>.heating-monitoring-right-top-panel {
    height: 200px !important;
    max-height: 200px !important;
  }

  .right-panel>.heating-monitoring-right-middle-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  .right-panel>.heating-monitoring-right-bottom-panel {
    height: 420px !important;
    max-height: 420px !important;
  }

  /* 排水专项-综合态势右侧面板高度适配 */
  .right-panel>.drainage-overview-right-top-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  .right-panel>.drainage-overview-right-middle-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  .right-panel>.drainage-overview-right-bottom-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  /* 桥梁专项面板高度 */
  .left-panel>.bridge-left-top-panel {
    --panel-height: 280px;
  }

  .left-panel>.bridge-left-middle-panel {
    --panel-height: 320px;
  }

  .left-panel>.bridge-left-bottom-panel {
    --panel-height: 320px;
  }

  /* 桥梁详情专项面板高度 */
  .left-panel>.bridge-detail-left-top-panel {
    --panel-height: 280px;
  }

  .left-panel>.bridge-detail-left-middle-panel {
    --panel-height: 320px;
  }

  .left-panel>.bridge-detail-left-bottom-panel {
    --panel-height: 320px;
  }

  .right-panel>.bridge-right-top-panel {
    height: 320px !important;
    max-height: 320px !important;
  }

  .right-panel>.bridge-right-middle-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  .right-panel>.bridge-right-bottom-panel {
    height: 300px !important;
    max-height: 300px !important;
  }

  /* 桥梁详情专项右侧面板高度 */
  .right-panel>.bridge-detail-right-top-panel {
    height: 320px !important;
    max-height: 320px !important;
  }

  .right-panel>.bridge-detail-right-middle-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  .right-panel>.bridge-detail-right-bottom-panel {
    height: 300px !important;
    max-height: 300px !important;
  }

  /* 综合专项 - 综合态势左侧面板高度 */
  .left-panel>.comprehensive-overview-left-top-panel {
    height: 320px !important;
    max-height: 320px !important;
  }

  .left-panel>.comprehensive-overview-left-middle-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  .left-panel>.comprehensive-overview-left-bottom-panel {
    height: 300px !important;
    max-height: 300px !important;
  }

  /* 综合专项 - 综合态势右侧面板高度 */
  .right-panel>.comprehensive-overview-right-top-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  .right-panel>.comprehensive-overview-right-middle-panel {
    height: 300px !important;
    max-height: 300px !important;
  }

  .right-panel>.comprehensive-overview-right-bottom-panel {
    height: 320px !important;
    max-height: 320px !important;
  }

  /* 综合专项 - 综合风险隐患左侧面板高度 */
  .left-panel>.comprehensive-risk-left-top-panel {
    height: 320px !important;
    max-height: 320px !important;
  }

  .left-panel>.comprehensive-risk-left-middle-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  .left-panel>.comprehensive-risk-left-bottom-panel {
    height: 300px !important;
    max-height: 300px !important;
  }

  /* 综合专项 - 综合风险隐患右侧面板高度 */
  .right-panel>.comprehensive-risk-right-top-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  .right-panel>.comprehensive-risk-right-middle-panel {
    height: 300px !important;
    max-height: 300px !important;
  }

  .right-panel>.comprehensive-risk-right-bottom-panel {
    height: 320px !important;
    max-height: 320px !important;
  }

  /* 综合专项 - 综合运行监测左侧面板高度 */
  .left-panel>.comprehensive-monitoring-left-top-panel {
    height: 280px !important;
    max-height: 280px !important;
  }

  .left-panel>.comprehensive-monitoring-left-middle-panel {
    height: 330px !important;
    max-height: 330px !important;
  }

  .left-panel>.comprehensive-monitoring-left-bottom-panel {
    height: 320px !important;
    max-height: 320px !important;
  }

  /* 综合专项 - 综合运行监测右侧面板高度 */
  .right-panel>.comprehensive-monitoring-right-top-panel {
    height: 320px !important;
    max-height: 320px !important;
  }

  .right-panel>.comprehensive-monitoring-right-middle-panel {
    height: 300px !important;
    max-height: 300px !important;
  }

  .right-panel>.comprehensive-monitoring-right-bottom-panel {
    height: 290px !important;
    max-height: 290px !important;
  }

  /* 综合专项 - 协同联动处置左侧面板高度 */
  .left-panel>.comprehensive-coordination-left-top-panel, .left-panel>.comprehensive-coordination-left-middle-panel, .left-panel>.comprehensive-coordination-left-bottom-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  /* 综合专项 - 协同联动处置右侧面板高度 */
  .right-panel>.comprehensive-coordination-right-top-panel {
    height: 310px !important;
    max-height: 310px !important;
  }

  .right-panel>.comprehensive-coordination-right-middle-panel {
    height: 620px !important;
    max-height: 620px !important;
  }

  /* 综合专项 - 应急辅助决策左侧面板高度 */
  .left-panel>.comprehensive-emergency-left-top-panel {
    height: 330px !important;
    max-height: 330px !important;
  }

  .left-panel>.comprehensive-emergency-left-middle-panel {
    height: 280px !important;
    max-height: 280px !important;
  }

  .left-panel>.comprehensive-emergency-left-bottom-panel {
    height: 320px !important;
    max-height: 320px !important;
  }

  /* 综合专项 - 应急辅助决策右侧面板高度 */
  .right-panel>.comprehensive-emergency-right-top-panel {
    height: 280px !important;
    max-height: 280px !important;
  }

  .right-panel>.comprehensive-emergency-right-middle-panel {
    height: 300px !important;
    max-height: 300px !important;
  }

  .right-panel>.comprehensive-emergency-right-bottom-panel {
    height: 350px !important;
    max-height: 350px !important;
  }
}

@media (min-width: 2561px) and (max-width: 3440px) {

  /* 21:9 的超宽屏 */
  .main-content {
    grid-template-columns: 472px minmax(0, 1fr) 472px;
    padding: 0 50px;
  }

  .screen-sub-nav-container {
    margin: 10px 40px;
  }

  .panel-bottom-space {
    height: 50px;
    /* 保持与底部菜单高度一致 */
  }

  /* 右侧面板高度设置 - 超宽屏幕下稍微增加高度 */
  .right-top-panel {
    height: calc(280px * 1.15) !important;
    max-height: calc(280px * 1.15) !important;
  }

  .right-middle-panel {
    height: calc(310px * 1.15) !important;
    max-height: calc(310px * 1.15) !important;
  }

  .right-bottom-panel {
    height: calc(340px * 1.15) !important;
    max-height: calc(340px * 1.15) !important;
  }

  /* 供热专项 - 供热风险隐患右侧面板高度适配 */
  .right-panel>.heating-risk-right-top-panel {
    height: calc(340px * 1.15) !important;
    max-height: calc(340px * 1.15) !important;
  }

  .right-panel>.heating-risk-right-middle-panel {
    height: calc(310px * 1.15) !important;
    max-height: calc(310px * 1.15) !important;
  }

  .right-panel>.heating-risk-right-bottom-panel {
    height: calc(280px * 1.15) !important;
    max-height: calc(280px * 1.15) !important;
  }

  /* 供热专项 - 供热监测报警左侧面板高度 */
  .left-panel>.heating-monitoring-left-top-panel,
  .left-panel>.heating-monitoring-left-middle-panel,
  .left-panel>.heating-monitoring-left-bottom-panel {
    height: calc(310px * 1.15) !important;
    max-height: calc(310px * 1.15) !important;
  }

  /* 供热专项 - 供热监测报警右侧面板高度 */
  .right-panel>.heating-monitoring-right-top-panel {
    height: calc(200px * 1.15) !important;
    max-height: calc(200px * 1.15) !important;
  }

  .right-panel>.heating-monitoring-right-middle-panel {
    height: calc(310px * 1.15) !important;
    max-height: calc(310px * 1.15) !important;
  }

  .right-panel>.heating-monitoring-right-bottom-panel {
    height: calc(420px * 1.15) !important;
    max-height: calc(420px * 1.15) !important;
  }

  /* 综合专项 - 综合态势左侧面板高度 */
  .left-panel>.comprehensive-overview-left-top-panel {
    height: calc(320px * 1.15) !important;
    max-height: calc(320px * 1.15) !important;
  }

  .left-panel>.comprehensive-overview-left-middle-panel {
    height: calc(310px * 1.15) !important;
    max-height: calc(310px * 1.15) !important;
  }

  .left-panel>.comprehensive-overview-left-bottom-panel {
    height: calc(300px * 1.15) !important;
    max-height: calc(300px * 1.15) !important;
  }

  /* 综合专项 - 综合态势右侧面板高度 */
  .right-panel>.comprehensive-overview-right-top-panel {
    height: calc(310px * 1.15) !important;
    max-height: calc(310px * 1.15) !important;
  }

  .right-panel>.comprehensive-overview-right-middle-panel {
    height: calc(300px * 1.15) !important;
    max-height: calc(300px * 1.15) !important;
  }

  .right-panel>.comprehensive-overview-right-bottom-panel {
    height: calc(320px * 1.15) !important;
    max-height: calc(320px * 1.15) !important;
  }

  /* 综合专项 - 综合风险隐患左侧面板高度 */
  .left-panel>.comprehensive-risk-left-top-panel {
    height: calc(320px * 1.15) !important;
    max-height: calc(320px * 1.15) !important;
  }

  .left-panel>.comprehensive-risk-left-middle-panel {
    height: calc(310px * 1.15) !important;
    max-height: calc(310px * 1.15) !important;
  }

  .left-panel>.comprehensive-risk-left-bottom-panel {
    height: calc(300px * 1.15) !important;
    max-height: calc(300px * 1.15) !important;
  }

  /* 综合专项 - 综合风险隐患右侧面板高度 */
  .right-panel>.comprehensive-risk-right-top-panel {
    height: calc(310px * 1.15) !important;
    max-height: calc(310px * 1.15) !important;
  }

  .right-panel>.comprehensive-risk-right-middle-panel {
    height: calc(300px * 1.15) !important;
    max-height: calc(300px * 1.15) !important;
  }

  .right-panel>.comprehensive-risk-right-bottom-panel {
    height: calc(320px * 1.15) !important;
    max-height: calc(320px * 1.15) !important;
  }

  /* 综合专项 - 综合运行监测左侧面板高度 */
  .left-panel>.comprehensive-monitoring-left-top-panel {
    height: calc(280px * 1.15) !important;
    max-height: calc(280px * 1.15) !important;
  }

  .left-panel>.comprehensive-monitoring-left-middle-panel {
    height: calc(330px * 1.15) !important;
    max-height: calc(330px * 1.15) !important;
  }

  .left-panel>.comprehensive-monitoring-left-bottom-panel {
    height: calc(320px * 1.15) !important;
    max-height: calc(320px * 1.15) !important;
  }

  /* 综合专项 - 综合运行监测右侧面板高度 */
  .right-panel>.comprehensive-monitoring-right-top-panel {
    height: calc(320px * 1.15) !important;
    max-height: calc(320px * 1.15) !important;
  }

  .right-panel>.comprehensive-monitoring-right-middle-panel {
    height: calc(300px * 1.15) !important;
    max-height: calc(300px * 1.15) !important;
  }

  .right-panel>.comprehensive-monitoring-right-bottom-panel {
    height: calc(290px * 1.15) !important;
    max-height: calc(290px * 1.15) !important;
  }

  /* 综合专项 - 协同联动处置左侧面板高度 */
  .left-panel>.comprehensive-coordination-left-top-panel, .left-panel>.comprehensive-coordination-left-middle-panel, .left-panel>.comprehensive-coordination-left-bottom-panel {
    height: calc(310px * 1.15) !important;
    max-height: calc(310px * 1.15) !important;
  }

  /* 综合专项 - 协同联动处置右侧面板高度 */
  .right-panel>.comprehensive-coordination-right-top-panel {
    height: calc(310px * 1.15) !important;
    max-height: calc(310px * 1.15) !important;
  }

  .right-panel>.comprehensive-coordination-right-middle-panel {
    height: calc(620px * 1.15) !important;
    max-height: calc(620px * 1.15) !important;
  }


  /* 综合专项 - 应急辅助决策左侧面板高度 */
  .left-panel>.comprehensive-emergency-left-top-panel {
    height: calc(330px * 1.15) !important;
    max-height: calc(330px * 1.15) !important;
  }

  .left-panel>.comprehensive-emergency-left-middle-panel {
    height: calc(280px * 1.15) !important;
    max-height: calc(280px * 1.15) !important;
  }

  .left-panel>.comprehensive-emergency-left-bottom-panel {
    height: calc(320px * 1.15) !important;
    max-height: calc(320px * 1.15) !important;
  }

  /* 综合专项 - 应急辅助决策右侧面板高度 */
  .right-panel>.comprehensive-emergency-right-top-panel {
    height: calc(280px * 1.15) !important;
    max-height: calc(280px * 1.15) !important;
  }

  .right-panel>.comprehensive-emergency-right-middle-panel {
    height: calc(300px * 1.15) !important;
    max-height: calc(300px * 1.15) !important;
  }

  .right-panel>.comprehensive-emergency-right-bottom-panel {
    height: calc(350px * 1.15) !important;
    max-height: calc(350px * 1.15) !important;
  }
}

@media (min-width: 3441px) {

  /* 32:9 的超宽屏 */
  .main-content {
    grid-template-columns: 472px minmax(0, 1fr) 472px;
    padding: 0 60px;
  }

  .screen-sub-nav-container {
    margin: 10px 50px;
  }

  .panel-bottom-space {
    height: 50px;
    /* 保持与底部菜单高度一致 */
  }

  /* 右侧面板高度设置 - 超宽屏幕下更大的高度 */
  .right-top-panel {
    height: calc(280px * 1.3) !important;
    max-height: calc(280px * 1.3) !important;
  }

  .right-middle-panel {
    height: calc(310px * 1.3) !important;
    max-height: calc(310px * 1.3) !important;
  }

  .right-bottom-panel {
    height: calc(340px * 1.3) !important;
    max-height: calc(340px * 1.3) !important;
  }

  /* 供热专项 - 供热风险隐患右侧面板高度适配 */
  .right-panel>.heating-risk-right-top-panel {
    height: calc(340px * 1.3) !important;
    max-height: calc(340px * 1.3) !important;
  }

  .right-panel>.heating-risk-right-middle-panel {
    height: calc(310px * 1.3) !important;
    max-height: calc(310px * 1.3) !important;
  }

  .right-panel>.heating-risk-right-bottom-panel {
    height: calc(280px * 1.3) !important;
    max-height: calc(280px * 1.3) !important;
  }

  /* 供热专项 - 供热监测报警左侧面板高度 */
  .left-panel>.heating-monitoring-left-top-panel,
  .left-panel>.heating-monitoring-left-middle-panel,
  .left-panel>.heating-monitoring-left-bottom-panel {
    height: calc(310px * 1.3) !important;
    max-height: calc(310px * 1.3) !important;
  }

  /* 供热专项 - 供热监测报警右侧面板高度 */
  .right-panel>.heating-monitoring-right-top-panel {
    height: calc(200px * 1.3) !important;
    max-height: calc(200px * 1.3) !important;
  }

  .right-panel>.heating-monitoring-right-middle-panel {
    height: calc(310px * 1.3) !important;
    max-height: calc(310px * 1.3) !important;
  }

  .right-panel>.heating-monitoring-right-bottom-panel {
    height: calc(420px * 1.3) !important;
    max-height: calc(420px * 1.3) !important;
  }

  /* 综合专项 - 综合态势左侧面板高度 */
  .left-panel>.comprehensive-overview-left-top-panel {
    height: calc(320px * 1.3) !important;
    max-height: calc(320px * 1.3) !important;
  }

  .left-panel>.comprehensive-overview-left-middle-panel {
    height: calc(310px * 1.3) !important;
    max-height: calc(310px * 1.3) !important;
  }

  .left-panel>.comprehensive-overview-left-bottom-panel {
    height: calc(300px * 1.3) !important;
    max-height: calc(300px * 1.3) !important;
  }

  /* 综合专项 - 综合态势右侧面板高度 */
  .right-panel>.comprehensive-overview-right-top-panel {
    height: calc(310px * 1.3) !important;
    max-height: calc(310px * 1.3) !important;
  }

  .right-panel>.comprehensive-overview-right-middle-panel {
    height: calc(300px * 1.3) !important;
    max-height: calc(300px * 1.3) !important;
  }

  .right-panel>.comprehensive-overview-right-bottom-panel {
    height: calc(320px * 1.3) !important;
    max-height: calc(320px * 1.3) !important;
  }

  /* 综合专项 - 综合风险隐患左侧面板高度 */
  .left-panel>.comprehensive-risk-left-top-panel {
    height: calc(320px * 1.3) !important;
    max-height: calc(320px * 1.3) !important;
  }

  .left-panel>.comprehensive-risk-left-middle-panel {
    height: calc(310px * 1.3) !important;
    max-height: calc(310px * 1.3) !important;
  }

  .left-panel>.comprehensive-risk-left-bottom-panel {
    height: calc(300px * 1.3) !important;
    max-height: calc(300px * 1.3) !important;
  }

  /* 综合专项 - 综合风险隐患右侧面板高度 */
  .right-panel>.comprehensive-risk-right-top-panel {
    height: calc(310px * 1.3) !important;
    max-height: calc(310px * 1.3) !important;
  }

  .right-panel>.comprehensive-risk-right-middle-panel {
    height: calc(300px * 1.3) !important;
    max-height: calc(300px * 1.3) !important;
  }

  .right-panel>.comprehensive-risk-right-bottom-panel {
    height: calc(320px * 1.3) !important;
    max-height: calc(320px * 1.3) !important;
  }

  /* 综合专项 - 综合运行监测左侧面板高度 */
  .left-panel>.comprehensive-monitoring-left-top-panel {
    height: calc(280px * 1.3) !important;
    max-height: calc(280px * 1.3) !important;
  }

  .left-panel>.comprehensive-monitoring-left-middle-panel {
    height: calc(330px * 1.3) !important;
    max-height: calc(330px * 1.3) !important;
  }

  .left-panel>.comprehensive-monitoring-left-bottom-panel {
    height: calc(320px * 1.3) !important;
    max-height: calc(320px * 1.3) !important;
  }

  /* 综合专项 - 综合运行监测右侧面板高度 */
  .right-panel>.comprehensive-monitoring-right-top-panel {
    height: calc(320px * 1.3) !important;
    max-height: calc(320px * 1.3) !important;
  }

  .right-panel>.comprehensive-monitoring-right-middle-panel {
    height: calc(300px * 1.3) !important;
    max-height: calc(300px * 1.3) !important;
  }

  .right-panel>.comprehensive-monitoring-right-bottom-panel {
    height: calc(290px * 1.3) !important;
    max-height: calc(290px * 1.3) !important;
  }

  /* 综合专项 - 协同联动处置左侧面板高度 */
  .left-panel>.comprehensive-coordination-left-top-panel, .left-panel>.comprehensive-coordination-left-middle-panel, .left-panel>.comprehensive-coordination-left-bottom-panel {
    height: calc(310px * 1.3) !important;
    max-height: calc(310px * 1.3) !important;
  }

  /* 综合专项 - 协同联动处置右侧面板高度 */
  .right-panel>.comprehensive-coordination-right-top-panel {
    height: calc(310px * 1.3) !important;
    max-height: calc(310px * 1.3) !important;
  }

  .right-panel>.comprehensive-coordination-right-middle-panel {
    height: calc(620px * 1.3) !important;
    max-height: calc(620px * 1.3) !important;
  }
  /* 综合专项 - 应急辅助决策左侧面板高度 */
  .left-panel>.comprehensive-emergency-left-top-panel {
    height: calc(330px * 1.3) !important;
    max-height: calc(330px * 1.3) !important;
  }

  .left-panel>.comprehensive-emergency-left-middle-panel {
    height: calc(280px * 1.3) !important;
    max-height: calc(280px * 1.3) !important;
  }

  .left-panel>.comprehensive-emergency-left-bottom-panel {
    height: calc(320px * 1.3) !important;
    max-height: calc(320px * 1.3) !important;
  }

  /* 综合专项 - 应急辅助决策右侧面板高度 */
  .right-panel>.comprehensive-emergency-right-top-panel {
    height: calc(280px * 1.3) !important;
    max-height: calc(280px * 1.3) !important;
  }

  .right-panel>.comprehensive-emergency-right-middle-panel {
    height: calc(300px * 1.3) !important;
    max-height: calc(300px * 1.3) !important;
  }

  .right-panel>.comprehensive-emergency-right-bottom-panel {
    height: calc(350px * 1.3) !important;
    max-height: calc(350px * 1.3) !important;
  }
}

/* 针对高度较低的屏幕进行优化 */
@media (max-height: 900px) {
  .main-content {
    gap: 8px;
    margin-top: -25px;
  }

  .screen-sub-nav-container {
    height: 36px;
    margin: 5px 20px 2px 20px;
  }

  .main-content {
    padding: 0 20px;
  }

  .left-panel,
  .right-panel {
    space-y: 8px;
  }

  /* 右侧面板高度设置 - 小屏幕下减少高度 */
  .right-top-panel {
    height: calc(280px * 0.85) !important;
    max-height: calc(280px * 0.85) !important;
  }

  .right-middle-panel {
    height: calc(310px * 0.85) !important;
    max-height: calc(310px * 0.85) !important;
  }

  .right-bottom-panel {
    height: calc(340px * 0.85) !important;
    max-height: calc(340px * 0.85) !important;
  }

  .panel-bottom-space {
    height: 40px;
    /* 降低底部间距 */
  }

  .footer-nav-container {
    height: 60px;
    /* 调整底部菜单高度 */
  }

  /* 供热专项 - 供热风险隐患左侧面板高度适配 */
  .left-panel>.heating-overview-left-top-panel {
    --panel-height: calc(340px * 0.85);
  }

  .left-panel>.heating-overview-left-middle-panel {
    --panel-height: calc(260px * 0.85);
  }

  .left-panel>.heating-overview-left-bottom-panel {
    --panel-height: calc(320px * 0.85);
  }

  /* 供热专项 - 综合态势右侧面板高度适配 */
  .right-panel>.heating-overview-right-top-panel {
    height: calc(310px * 0.85) !important;
    max-height: calc(310px * 0.85) !important;
  }

  .right-panel>.heating-overview-right-middle-panel {
    height: calc(310px * 0.85) !important;
    max-height: calc(310px * 0.85) !important;
  }

  .right-panel>.heating-overview-right-bottom-panel {
    height: calc(310px * 0.85) !important;
    max-height: calc(310px * 0.85) !important;
  }

  /* 桥梁专项面板高度适配 - 小屏幕 */
  .left-panel>.bridge-left-top-panel {
    --panel-height: calc(280px * 0.85);
  }

  .left-panel>.bridge-left-middle-panel {
    --panel-height: calc(320px * 0.85);
  }

  .left-panel>.bridge-left-bottom-panel {
    --panel-height: calc(320px * 0.85);
  }

  /* 桥梁详情专项面板高度适配 - 小屏幕 */
  .left-panel>.bridge-detail-left-top-panel {
    --panel-height: calc(280px * 0.85);
  }

  .left-panel>.bridge-detail-left-middle-panel {
    --panel-height: calc(320px * 0.85);
  }

  .left-panel>.bridge-detail-left-bottom-panel {
    --panel-height: calc(320px * 0.85);
  }

  .right-panel>.bridge-right-top-panel {
    height: calc(320px * 0.85) !important;
    max-height: calc(320px * 0.85) !important;
  }

  .right-panel>.bridge-right-middle-panel {
    height: calc(310px * 0.85) !important;
    max-height: calc(310px * 0.85) !important;
  }

  .right-panel>.bridge-right-bottom-panel {
    height: calc(300px * 0.85) !important;
    max-height: calc(300px * 0.85) !important;
  }

  /* 桥梁详情专项右侧面板高度适配 - 小屏幕 */
  .right-panel>.bridge-detail-right-top-panel {
    height: calc(320px * 0.85) !important;
    max-height: calc(320px * 0.85) !important;
  }

  .right-panel>.bridge-detail-right-middle-panel {
    height: calc(310px * 0.85) !important;
    max-height: calc(310px * 0.85) !important;
  }

  .right-panel>.bridge-detail-right-bottom-panel {
    height: calc(300px * 0.85) !important;
    max-height: calc(300px * 0.85) !important;
  }

  /* 供热专项 - 供热风险隐患左侧面板高度适配 */
  .left-panel>.heating-risk-left-top-panel {
    --panel-height: calc(280px * 0.85);
  }

  .left-panel>.heating-risk-left-middle-panel {
    --panel-height: calc(320px * 0.85);
  }

  .left-panel>.heating-risk-left-bottom-panel {
    --panel-height: calc(320px * 0.85);
  }

  /* 供热专项 - 供热风险隐患右侧面板高度适配 */
  .right-panel>.heating-risk-right-top-panel {
    height: calc(340px * 0.85) !important;
    max-height: calc(340px * 0.85) !important;
  }

  .right-panel>.heating-risk-right-middle-panel {
    height: calc(310px * 0.85) !important;
    max-height: calc(310px * 0.85) !important;
  }

  .right-panel>.heating-risk-right-bottom-panel {
    height: calc(280px * 0.85) !important;
    max-height: calc(280px * 0.85) !important;
  }

  /* 供热专项 - 供热监测报警左侧面板高度 */
  .left-panel>.heating-monitoring-left-top-panel,
  .left-panel>.heating-monitoring-left-middle-panel,
  .left-panel>.heating-monitoring-left-bottom-panel {
    height: calc(310px * 0.85) !important;
    max-height: calc(310px * 0.85) !important;
  }

  /* 供热专项 - 供热监测报警右侧面板高度 */
  .right-panel>.heating-monitoring-right-top-panel {
    height: calc(200px * 0.85) !important;
    max-height: calc(200px * 0.85) !important;
  }

  .right-panel>.heating-monitoring-right-middle-panel {
    height: calc(310px * 0.85) !important;
    max-height: calc(310px * 0.85) !important;
  }

  .right-panel>.heating-monitoring-right-bottom-panel {
    height: calc(420px * 0.85) !important;
    max-height: calc(420px * 0.85) !important;
  }

  /* 综合专项 - 综合态势左侧面板高度 */
  .left-panel>.comprehensive-overview-left-top-panel {
    height: calc(320px * 0.85) !important;
    max-height: calc(320px * 0.85) !important;
  }

  .left-panel>.comprehensive-overview-left-middle-panel {
    height: calc(310px * 0.85) !important;
    max-height: calc(310px * 0.85) !important;
  }

  .left-panel>.comprehensive-overview-left-bottom-panel {
    height: calc(300px * 0.85) !important;
    max-height: calc(300px * 0.85) !important;
  }

  /* 综合专项 - 综合态势右侧面板高度 */
  .right-panel>.comprehensive-overview-right-top-panel {
    height: calc(310px * 0.85) !important;
    max-height: calc(310px * 0.85) !important;
  }

  .right-panel>.comprehensive-overview-right-middle-panel {
    height: calc(300px * 0.85) !important;
    max-height: calc(300px * 0.85) !important;
  }

  .right-panel>.comprehensive-overview-right-bottom-panel {
    height: calc(320px * 0.85) !important;
    max-height: calc(320px * 0.85) !important;
  }

  /* 综合专项 - 综合风险隐患左侧面板高度 */
  .left-panel>.comprehensive-risk-left-top-panel {
    height: calc(320px * 0.85) !important;
    max-height: calc(320px * 0.85) !important;
  }

  .left-panel>.comprehensive-risk-left-middle-panel {
    height: calc(310px * 0.85) !important;
    max-height: calc(310px * 0.85) !important;
  }

  .left-panel>.comprehensive-risk-left-bottom-panel {
    height: calc(300px * 0.85) !important;
    max-height: calc(300px * 0.85) !important;
  }

  /* 综合专项 - 综合风险隐患右侧面板高度 */
  .right-panel>.comprehensive-risk-right-top-panel {
    height: calc(310px * 0.85) !important;
    max-height: calc(310px * 0.85) !important;
  }

  .right-panel>.comprehensive-risk-right-middle-panel {
    height: calc(300px * 0.85) !important;
    max-height: calc(300px * 0.85) !important;
  }

  .right-panel>.comprehensive-risk-right-bottom-panel {
    height: calc(320px * 0.85) !important;
    max-height: calc(320px * 0.85) !important;
  }

  /* 综合专项 - 综合运行监测左侧面板高度 */
  .left-panel>.comprehensive-monitoring-left-top-panel {
    height: calc(280px * 0.85) !important;
    max-height: calc(280px * 0.85) !important;
  }

  .left-panel>.comprehensive-monitoring-left-middle-panel {
    height: calc(330px * 0.85) !important;
    max-height: calc(330px * 0.85) !important;
  }

  .left-panel>.comprehensive-monitoring-left-bottom-panel {
    height: calc(320px * 0.85) !important;
    max-height: calc(320px * 0.85) !important;
  }

  /* 综合专项 - 综合运行监测右侧面板高度 */
  .right-panel>.comprehensive-monitoring-right-top-panel {
    height: calc(320px * 0.85) !important;
    max-height: calc(320px * 0.85) !important;
  }

  .right-panel>.comprehensive-monitoring-right-middle-panel {
    height: calc(300px * 0.85) !important;
    max-height: calc(300px * 0.85) !important;
  }

  .right-panel>.comprehensive-monitoring-right-bottom-panel {
    height: calc(290px * 0.85) !important;
    max-height: calc(290px * 0.85) !important;
  }

  /* 综合专项 - 协同联动处置左侧面板高度 */
  .left-panel>.comprehensive-coordination-left-top-panel, .left-panel>.comprehensive-coordination-left-middle-panel, .left-panel>.comprehensive-coordination-left-bottom-panel {
    height: calc(310px * 0.85) !important;
    max-height: calc(310px * 0.85) !important;
  }

  /* 综合专项 - 协同联动处置右侧面板高度 */
  .right-panel>.comprehensive-coordination-right-top-panel {
    height: calc(310px * 0.85) !important;
    max-height: calc(310px * 0.85) !important;
  }

  .right-panel>.comprehensive-coordination-right-middle-panel {
    height: calc(620px * 0.85) !important;
    max-height: calc(620px * 0.85) !important;
  }

  /* 综合专项 - 应急辅助决策左侧面板高度 */
  .left-panel>.comprehensive-emergency-left-top-panel {
    height: calc(330px * 0.85) !important;
    max-height: calc(330px * 0.85) !important;
  }

  .left-panel>.comprehensive-emergency-left-middle-panel {
    height: calc(280px * 0.85) !important;
    max-height: calc(280px * 0.85) !important;
  }

  .left-panel>.comprehensive-emergency-left-bottom-panel {
    height: calc(320px * 0.85) !important;
    max-height: calc(320px * 0.85) !important;
  }

  /* 综合专项 - 应急辅助决策右侧面板高度 */
  .right-panel>.comprehensive-emergency-right-top-panel {
    height: calc(280px * 0.85) !important;
    max-height: calc(280px * 0.85) !important;
  }

  .right-panel>.comprehensive-emergency-right-middle-panel {
    height: calc(300px * 0.85) !important;
    max-height: calc(300px * 0.85) !important;
  }

  .right-panel>.comprehensive-emergency-right-bottom-panel {
    height: calc(350px * 0.85) !important;
    max-height: calc(350px * 0.85) !important;
  }
}

/* 940px左右高度的屏幕特别优化 */
@media (min-height: 900px) and (max-height: 1055px) {

  /* 进一步减少顶部元素占用空间 */
  .screen-sub-nav-container {
    height: 30px;
    margin: 2px 20px 0px 20px;
  }

  .main-content {
    margin-top: -35px;
  }

  /* 顶部Header区域压缩 */
  .screen-content>header {
    transform: scale(0.95);
    transform-origin: top center;
    margin-bottom: -10px;
  }

  .panel-bottom-space {
    height: 30px;
    /* 进一步降低底部间距 */
  }

  .footer-nav-container {
    height: 50px;
    /* 进一步调整底部菜单高度 */
  }

  /* 供热专项 - 综合态势左侧面板高度适配 - 中等屏幕 */
  .left-panel>.heating-overview-left-top-panel {
    --panel-height: calc(340px * 0.9);
  }

  .left-panel>.heating-overview-left-middle-panel {
    --panel-height: calc(260px * 0.9);
  }

  .left-panel>.heating-overview-left-bottom-panel {
    --panel-height: calc(320px * 0.9);
  }

  /* 供热专项 - 综合态势右侧面板高度适配 - 中等屏幕 */
  .right-panel>.heating-overview-right-top-panel {
    height: calc(310px * 0.9) !important;
    max-height: calc(310px * 0.9) !important;
  }

  .right-panel>.heating-overview-right-middle-panel {
    height: calc(310px * 0.9) !important;
    max-height: calc(310px * 0.9) !important;
  }

  .right-panel>.heating-overview-right-bottom-panel {
    height: calc(310px * 0.9) !important;
    max-height: calc(310px * 0.9) !important;
  }

  /* 桥梁专项面板高度适配 - 中等屏幕 */
  .left-panel>.bridge-left-top-panel {
    --panel-height: calc(280px * 0.9);
  }

  .left-panel>.bridge-left-middle-panel {
    --panel-height: calc(320px * 0.9);
  }

  .left-panel>.bridge-left-bottom-panel {
    --panel-height: calc(320px * 0.9);
  }

  /* 桥梁详情专项面板高度适配 - 中等屏幕 */
  .left-panel>.bridge-detail-left-top-panel {
    --panel-height: calc(280px * 0.9);
  }

  .left-panel>.bridge-detail-left-middle-panel {
    --panel-height: calc(320px * 0.9);
  }

  .left-panel>.bridge-detail-left-bottom-panel {
    --panel-height: calc(320px * 0.9);
  }

  .right-panel>.bridge-right-top-panel {
    height: calc(320px * 0.9) !important;
    max-height: calc(320px * 0.9) !important;
  }

  .right-panel>.bridge-right-middle-panel {
    height: calc(310px * 0.9) !important;
    max-height: calc(310px * 0.9) !important;
  }

  .right-panel>.bridge-right-bottom-panel {
    height: calc(300px * 0.9) !important;
    max-height: calc(300px * 0.9) !important;
  }

  /* 桥梁详情专项右侧面板高度适配 - 中等屏幕 */
  .right-panel>.bridge-detail-right-top-panel {
    height: calc(320px * 0.9) !important;
    max-height: calc(320px * 0.9) !important;
  }

  .right-panel>.bridge-detail-right-middle-panel {
    height: calc(310px * 0.9) !important;
    max-height: calc(310px * 0.9) !important;
  }

  .right-panel>.bridge-detail-right-bottom-panel {
    height: calc(300px * 0.9) !important;
    max-height: calc(300px * 0.9) !important;
  }

  /* 供热专项 - 供热风险隐患左侧面板高度适配 - 中等屏幕 */
  .left-panel>.heating-risk-left-top-panel {
    --panel-height: calc(280px * 0.9);
  }

  .left-panel>.heating-risk-left-middle-panel {
    --panel-height: calc(320px * 0.9);
  }

  .left-panel>.heating-risk-left-bottom-panel {
    --panel-height: calc(320px * 0.9);
  }

  /* 供热专项 - 供热风险隐患右侧面板高度适配 - 中等屏幕 */
  .right-panel>.heating-risk-right-top-panel {
    height: calc(340px * 0.9) !important;
    max-height: calc(340px * 0.9) !important;
  }

  .right-panel>.heating-risk-right-middle-panel {
    height: calc(310px * 0.9) !important;
    max-height: calc(310px * 0.9) !important;
  }

  .right-panel>.heating-risk-right-bottom-panel {
    height: calc(280px * 0.9) !important;
    max-height: calc(280px * 0.9) !important;
  }

  /* 供热专项 - 供热监测报警左侧面板高度 */
  .left-panel>.heating-monitoring-left-top-panel,
  .left-panel>.heating-monitoring-left-middle-panel,
  .left-panel>.heating-monitoring-left-bottom-panel {
    height: calc(310px * 0.9) !important;
    max-height: calc(310px * 0.9) !important;
  }

  /* 供热专项 - 供热监测报警右侧面板高度 */
  .right-panel>.heating-monitoring-right-top-panel {
    height: calc(200px * 0.9) !important;
    max-height: calc(200px * 0.9) !important;
  }

  .right-panel>.heating-monitoring-right-middle-panel {
    height: calc(310px * 0.9) !important;
    max-height: calc(310px * 0.9) !important;
  }

  .right-panel>.heating-monitoring-right-bottom-panel {
    height: calc(420px * 0.9) !important;
    max-height: calc(420px * 0.9) !important;
  }

  /* 综合专项 - 综合态势左侧面板高度 */
  .left-panel>.comprehensive-overview-left-top-panel {
    height: calc(320px * 0.9) !important;
    max-height: calc(320px * 0.9) !important;
  }

  .left-panel>.comprehensive-overview-left-middle-panel {
    height: calc(310px * 0.9) !important;
    max-height: calc(310px * 0.9) !important;
  }

  .left-panel>.comprehensive-overview-left-bottom-panel {
    height: calc(300px * 0.9) !important;
    max-height: calc(300px * 0.9) !important;
  }

  /* 综合专项 - 综合态势右侧面板高度 */
  .right-panel>.comprehensive-overview-right-top-panel {
    height: calc(310px * 0.9) !important;
    max-height: calc(310px * 0.9) !important;
  }

  .right-panel>.comprehensive-overview-right-middle-panel {
    height: calc(300px * 0.9) !important;
    max-height: calc(300px * 0.9) !important;
  }

  .right-panel>.comprehensive-overview-right-bottom-panel {
    height: calc(320px * 0.9) !important;
    max-height: calc(320px * 0.9) !important;
  }

  /* 综合专项 - 综合风险隐患左侧面板高度 */
  .left-panel>.comprehensive-risk-left-top-panel {
    height: calc(320px * 0.9) !important;
    max-height: calc(320px * 0.9) !important;
  }

  .left-panel>.comprehensive-risk-left-middle-panel {
    height: calc(310px * 0.9) !important;
    max-height: calc(310px * 0.9) !important;
  }

  .left-panel>.comprehensive-risk-left-bottom-panel {
    height: calc(300px * 0.9) !important;
    max-height: calc(300px * 0.9) !important;
  }

  /* 综合专项 - 综合风险隐患右侧面板高度 */
  .right-panel>.comprehensive-risk-right-top-panel {
    height: calc(310px * 0.9) !important;
    max-height: calc(310px * 0.9) !important;
  }

  .right-panel>.comprehensive-risk-right-middle-panel {
    height: calc(300px * 0.9) !important;
    max-height: calc(300px * 0.9) !important;
  }

  .right-panel>.comprehensive-risk-right-bottom-panel {
    height: calc(320px * 0.9) !important;
    max-height: calc(320px * 0.9) !important;
  }

  /* 综合专项 - 综合运行监测左侧面板高度 */
  .left-panel>.comprehensive-monitoring-left-top-panel {
    height: calc(280px * 0.9) !important;
    max-height: calc(280px * 0.9) !important;
  }

  .left-panel>.comprehensive-monitoring-left-middle-panel {
    height: calc(330px * 0.9) !important;
    max-height: calc(330px * 0.9) !important;
  }

  .left-panel>.comprehensive-monitoring-left-bottom-panel {
    height: calc(320px * 0.9) !important;
    max-height: calc(320px * 0.9) !important;
  }

  /* 综合专项 - 综合运行监测右侧面板高度 */
  .right-panel>.comprehensive-monitoring-right-top-panel {
    height: calc(320px * 0.9) !important;
    max-height: calc(320px * 0.9) !important;
  }

  .right-panel>.comprehensive-monitoring-right-middle-panel {
    height: calc(300px * 0.9) !important;
    max-height: calc(300px * 0.9) !important;
  }

  .right-panel>.comprehensive-monitoring-right-bottom-panel {
    height: calc(290px * 0.9) !important;
    max-height: calc(290px * 0.9) !important;
  }

  /* 综合专项 - 协同联动处置左侧面板高度 */
  .left-panel>.comprehensive-coordination-left-top-panel,
  .left-panel>.comprehensive-coordination-left-middle-panel,
  .left-panel>.comprehensive-coordination-left-bottom-panel {
    height: calc(310px * 0.9) !important;
    max-height: calc(310px * 0.9) !important;
  }

  /* 综合专项 - 协同联动处置右侧面板高度 */
  .right-panel>.comprehensive-coordination-right-top-panel {
    height: calc(310px * 0.9) !important;
    max-height: calc(310px * 0.9) !important;
  }

  .right-panel>.comprehensive-coordination-right-middle-panel {
    height: calc(620px * 0.9) !important;
    max-height: calc(620px * 0.9) !important;
  }

  /* 综合专项 - 应急辅助决策左侧面板高度 */
  .left-panel>.comprehensive-emergency-left-top-panel {
    height: calc(330px * 0.9) !important;
    max-height: calc(330px * 0.9) !important;
  }

  .left-panel>.comprehensive-emergency-left-middle-panel {
    height: calc(280px * 0.9) !important;
    max-height: calc(280px * 0.9) !important;
  }

  .left-panel>.comprehensive-emergency-left-bottom-panel {
    height: calc(320px * 0.9) !important;
    max-height: calc(320px * 0.9) !important;
  }

  /* 综合专项 - 应急辅助决策右侧面板高度 */
  .right-panel>.comprehensive-emergency-right-top-panel {
    height: calc(280px * 0.9) !important;
    max-height: calc(280px * 0.9) !important;
  }

  .right-panel>.comprehensive-emergency-right-middle-panel {
    height: calc(300px * 0.9) !important;
    max-height: calc(300px * 0.9) !important;
  }

  .right-panel>.comprehensive-emergency-right-bottom-panel {
    height: calc(350px * 0.9) !important;
    max-height: calc(350px * 0.9) !important;
  }
}

/* 侧边装饰边框样式 */
.side-decoration {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 28px;
  /* 根据实际图片宽度调整 */
  height: 80%;
  /* 设置高度,垂直居中 */
  z-index: 5;
  /* 确保在地图上层,但在内容下层 */
  background-image: url('@/assets/images/screen/screen_side_bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  pointer-events: auto;
}

.left-side {
  left: 0;
}

.right-side {
  right: 0;
  transform: translateY(-50%) scaleX(-1);
  /* 水平镜像翻转 */
}

/* 蒙版基础样式 */
.screen-mask {
  position: absolute;
  top: 0;
  height: 100%;
  width: 559px;
  /* 1920*1080分辨率下的宽度 */
  z-index: 5;
  /* 高于地图(z-index:1),低于面板内容(z-index:10) */
  pointer-events: none;
  /* 确保不影响地图和工具栏交互 */
  background-image: url('@/assets/images/screen/mengban.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

/* 左侧蒙版样式 */
.screen-mask.left-mask {
  left: 0;
  transform: scaleX(-1);
  /* 水平镜像处理 */
}

/* 右侧蒙版样式 */
.screen-mask.right-mask {
  right: 0;
}

/* 媒体查询 - 适配不同分辨率 */
@media (max-width: 1600px) {
  .screen-mask {
    width: 480px;
    /* 较低分辨率下缩小宽度 */
  }
}

@media (min-width: 1921px) {
  .screen-mask {
    width: 650px;
    /* 更高分辨率下增加宽度 */
  }
}

/* 适配低高度屏幕的样式 */
.screen-wrapper {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.screen-container {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 940px左右高度的屏幕特别优化 */
@media (min-height: 940px) and (max-height: 1055px) {

  /* 进一步减少顶部元素占用空间 */
  .screen-sub-nav-container {
    height: 30px;
    margin: 2px 20px 0px 20px;
  }

  .main-content {
    margin-top: -35px;
  }

  /* 顶部Header区域压缩 */
  .screen-content>header {
    transform: scale(0.95);
    transform-origin: top center;
    margin-bottom: -10px;
  }

  .panel-bottom-space {
    height: 30px;
    /* 进一步降低底部间距 */
  }

  .footer-nav-container {
    height: 50px;
    /* 进一步调整底部菜单高度 */
  }
}

/* 排水专项-综合态势右侧面板高度适配 */
.right-panel>.drainage-overview-right-top-panel {
  height: calc(310px * 1.15) !important;
  max-height: calc(310px * 1.15) !important;
}

/* 桥梁专项面板高度适配 - 超宽屏幕 */
.right-panel>.bridge-right-top-panel {
  height: calc(320px * 1.15) !important;
  max-height: calc(320px * 1.15) !important;
}

.right-panel>.bridge-right-middle-panel {
  height: calc(310px * 1.15) !important;
  max-height: calc(310px * 1.15) !important;
}

.right-panel>.bridge-right-bottom-panel {
  height: calc(300px * 1.15) !important;
  max-height: calc(300px * 1.15) !important;
}

/* 桥梁信息头部样式 */
.bridge-info-header {
  margin-bottom: 10px;
  z-index: 15;
  pointer-events: auto;
}

.bridge-name-container {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: linear-gradient(135deg, rgba(0, 242, 241, 0.1) 0%, rgba(0, 123, 255, 0.1) 100%);
  border: 1px solid rgba(0, 242, 241, 0.3);
  border-radius: 8px;
  cursor: pointer;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  min-width: 200px;
}

.bridge-name-container:hover {
  background: linear-gradient(135deg, rgba(0, 242, 241, 0.2) 0%, rgba(0, 123, 255, 0.2) 100%);
  border-color: rgba(0, 242, 241, 0.5);
  transform: translateX(-2px);
}

.back-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(0, 242, 241, 0.1);
  transition: all 0.3s ease;
}

.bridge-name-container:hover .back-icon {
  background: rgba(0, 242, 241, 0.2);
}

.bridge-name {
  font-size: 16px;
  font-weight: 600;
  color: #00F2F1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 桥梁返回按钮响应式设计 */
@media (max-width: 1600px) {
  .bridge-name-container {
    padding: 10px 16px;
    min-width: 180px;
  }

  .bridge-name {
    font-size: 14px;
  }
}

@media (max-height: 900px) {
  .bridge-name-container {
    padding: 8px 12px;
    min-width: 160px;
  }

  .bridge-name {
    font-size: 13px;
  }
}
</style>