<template>
  <div class="evaluation-container">
    <!-- 统计卡片区域 -->
    <div class="statistics-section">
      <!-- 企业评价排名 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3 class="chart-title">企业评价排名</h3>
          <div class="chart-tabs">
            <el-button 
              :type="rankType === 1 ? 'primary' : ''" 
              size="small"
              @click="handleRankTypeChange(1)"
            >
              月度
            </el-button>
            <el-button 
              :type="rankType === 2 ? 'primary' : ''" 
              size="small"
              @click="handleRankTypeChange(2)"
            >
              季度
            </el-button>
            <el-button 
              :type="rankType === 3 ? 'primary' : ''" 
              size="small"
              @click="handleRankTypeChange(3)"
            >
              年度
            </el-button>
          </div>
        </div>
        <div class="chart-content rank-content">
          <div 
            v-for="(item, index) in rankingData.slice(0, 6)" 
            :key="index"
            class="rank-item"
          >
            <div class="rank-number">
              <span class="rank-index" :class="getRankClass(index + 1)">{{ String(index + 1).padStart(2, '0') }}</span>
              <span class="rank-suffix">名</span>
            </div>
            <div class="enterprise-info">
              <div class="enterprise-name">{{ item.enterpriseName || '暂无数据' }}</div>
              <div class="enterprise-industry">{{ item.industryName || '燃气' }}</div>
            </div>
            <div class="score-container">
              <span class="score">{{ item.score || 90 }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 企业评价分布统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3 class="chart-title">企业评价分布统计</h3>
          <div class="chart-legend">
            <span class="legend-item legend-high">
              <span class="legend-dot"></span>
              81~100分
            </span>
            <span class="legend-item legend-medium">
              <span class="legend-dot"></span>
              61~80分
            </span>
            <span class="legend-item legend-low">
              <span class="legend-dot"></span>
              0~60分
            </span>
          </div>
        </div>
        <div class="chart-content distribution-content">
          <div ref="distributionChart" class="echarts-container"></div>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="form-item">
          <span class="label">考核周期:</span>
          <el-select v-model="searchForm.evaluationPeriod" class="form-input" placeholder="请选择考核周期">
            <el-option label="全部" value="" />
            <el-option v-for="item in evaluationPeriodOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">所属行业:</span>
          <el-select v-model="searchForm.relatedBusiness" class="form-input" placeholder="请选择所属行业">
            <el-option label="全部" value="" />
            <el-option v-for="item in relatedBusinessOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="searchForm.evaluationUnit" class="form-input" placeholder="输入企业名称" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
          <el-button type="primary" class="operation-btn" @click="handleAdd">新增</el-button>
        </div>
      </div>
    </div>

    <!-- 按钮区域 -->
    <!-- <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">新增</el-button>
      </div>
    </div> -->

    <!-- 表格区域 -->
    <el-table :data="tableData" style="width: 100%" :max-height="tableMaxHeight" :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName" @row-click="handleRowClick" :scrollbar-always-on="true" :fit="true" empty-text="暂无数据"
      v-loading="loading">
      <el-table-column label="序号" min-width="60">
        <template #default="{ $index }">
          {{ (currentPage - 1) * pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="evaluationUnit" label="企业名称" min-width="120" />
      <el-table-column prop="relatedBusinessName" label="所属行业" min-width="100" />
      <el-table-column prop="evaluationPeriodName" label="考核周期" min-width="100" />
      <el-table-column label="评分" min-width="80">
        <template #default="{ row }">
          <span :class="getScoreClass(row.score)">{{ row.score }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="评价描述" min-width="150" show-overflow-tooltip />
      <el-table-column prop="createTime" label="创建时间" min-width="120" />
      <el-table-column label="操作" fixed="right" min-width="200" align="center">
        <template #default="{ row }">
          <div class="operation-btns">
            <div class="operation-btn-row">
              <span class="operation-btn-text" @click.stop="handleDetail(row)">详情</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleEdit(row)">查看</span>
              <span class="operation-divider">|</span>
              <span class="operation-btn-text" @click.stop="handleDelete(row)">删除</span>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
  />
    </div>

    <!-- 评价弹窗 -->
    <EvaluationDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed, nextTick, onUnmounted } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import {
  getEvaluationPage,
  deleteEvaluation,
  getEvaluationDetail,
  getEvaluationRank,
  getEvaluationDistribution,
  EVALUATION_PERIOD_OPTIONS,
  RELATED_BUSINESS_OPTIONS
} from '@/api/comprehensive'
import EvaluationDialog from './components/EvaluationDialog.vue'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])
const loading = ref(false)
const tableMaxHeight = ref(160)

// 统计数据
const rankType = ref(1) // 1:月度 2:季度 3:年度
const rankingData = ref([])
const distributionData = ref([])

// ECharts实例
const distributionChart = ref(null)
let chartInstance = null

// 搜索表单
const searchForm = reactive({
  evaluationPeriod: '',
  relatedBusiness: '',
  evaluationUnit: ''
})

// 下拉选项
const evaluationPeriodOptions = ref(EVALUATION_PERIOD_OPTIONS)
const relatedBusinessOptions = ref(RELATED_BUSINESS_OPTIONS)

// 弹窗相关
const dialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit' | 'view'
const dialogData = ref({})

// 表格样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 获取排名样式类
const getRankClass = (rank) => {
  if (rank <= 3) return 'rank-top'
  return 'rank-normal'
}

// 获取评分样式类
const getScoreClass = (score) => {
  if (score >= 81) return 'score-high'
  if (score >= 61) return 'score-medium'
  return 'score-low'
}

// 初始化分布统计图表
const initDistributionChart = () => {
  if (!distributionChart.value) return

  chartInstance = echarts.init(distributionChart.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        let result = params[0].name + '<br/>'
        params.forEach(param => {
          result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>${param.seriesName}: ${param.value}<br/>`
        })
        return result
      }
    },
    legend: {
      show: false // 使用自定义图例
    },
    grid: {
      left: '8%',
      right: '4%',
      bottom: '10%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: [],
      axisTick: {
        alignWithLabel: true
      },
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      },
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#F5F7FA'
        }
      }
    },
    series: [
      {
        name: '81~100分',
        type: 'bar',
        barWidth: '20%',
        data: [],
        itemStyle: {
          color: '#409EFF'
        }
      },
      {
        name: '61~80分',
        type: 'bar',
        barWidth: '20%',
        data: [],
        itemStyle: {
          color: '#E6A23C'
        }
      },
      {
        name: '0~60分',
        type: 'bar',
        barWidth: '20%',
        data: [],
        itemStyle: {
          color: '#F56C6C'
        }
      }
    ]
  }
  
  chartInstance.setOption(option)
}

// 更新分布统计图表
const updateDistributionChart = (data) => {
  if (!chartInstance) return

  // 处理数据，按行业分组统计不同分数段的数量
  const industryMap = new Map()
  
  // 初始化所有行业
  const allIndustries = ['燃气', '排水', '供热', '桥梁']
  allIndustries.forEach(industry => {
    industryMap.set(industry, { high: 0, medium: 0, low: 0 })
  })

  // 如果有实际数据，处理统计信息
  if (Array.isArray(data) && data.length > 0) {
    // 根据实际API响应结构，这里的data是行业分类列表
    // 需要进一步调用统计接口获取每个行业的分数段统计
    data.forEach(item => {
      const industry = item.relatedBusinessName || '其他'
      if (allIndustries.includes(industry)) {
        // 这里需要根据实际的统计逻辑获取每个分数段的数量
        // 由于当前API返回的是行业列表，没有统计数据，使用模拟数据
        industryMap.set(industry, getDefaultStatsForIndustry(industry))
      }
    })
  } else {
    // 使用默认模拟数据
    industryMap.set('燃气', { high: 19, medium: 15, low: 6 })
    industryMap.set('排水', { high: 16, medium: 11, low: 4 })
    industryMap.set('供热', { high: 16, medium: 15, low: 5 })
    industryMap.set('桥梁', { high: 12, medium: 11, low: 3 })
  }

  const categories = Array.from(industryMap.keys())
  const highData = categories.map(cat => industryMap.get(cat).high)
  const mediumData = categories.map(cat => industryMap.get(cat).medium)
  const lowData = categories.map(cat => industryMap.get(cat).low)

  const option = {
    xAxis: {
      data: categories
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: Math.max(...highData, ...mediumData, ...lowData) + 5, // 动态设置最大值
      interval: 5 // 设置刻度间隔
    },
    series: [
      { data: highData },
      { data: mediumData },
      { data: lowData }
    ]
  }

  chartInstance.setOption(option, true)
}

// 获取行业默认统计数据
const getDefaultStatsForIndustry = (industry) => {
  const defaultStats = {
    '燃气': { high: 19, medium: 15, low: 6 },
    '排水': { high: 16, medium: 11, low: 4 },
    '供热': { high: 16, medium: 15, low: 5 },
    '桥梁': { high: 12, medium: 11, low: 3 }
  }
  return defaultStats[industry] || { high: 10, medium: 8, low: 3 }
}

// 处理排名类型切换
const handleRankTypeChange = (type) => {
  rankType.value = type
  fetchRankingData()
}

// 获取排名数据
const fetchRankingData = async () => {
  try {
    const res = await getEvaluationRank(rankType.value)
    if (res && res.code === 200) {
      rankingData.value = res.data || []
    }
  } catch (error) {
    console.error('获取排名数据失败:', error)
    // 使用模拟数据
    rankingData.value = [
      { enterpriseName: '燃气企业', industryName: '燃气', score: 90 },
      { enterpriseName: '供水企业', industryName: '排水', score: 85 },
      { enterpriseName: '排水企业', industryName: '排水', score: 80 },
      { enterpriseName: '供热企业', industryName: '供热', score: 75 },
      { enterpriseName: '桥梁企业', industryName: '桥梁', score: 70 },
      { enterpriseName: '桥梁企业', industryName: '桥梁', score: 70 }
    ]
  }
}

// 获取分布统计数据
const fetchDistributionData = async () => {
  try {
    const res = await getEvaluationDistribution()
    if (res && res.code === 200) {
      distributionData.value = res.data || []
      updateDistributionChart(res.data)
    }
  } catch (error) {
    console.error('获取分布统计数据失败:', error)
    updateDistributionChart([])
  }
}

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchEvaluationData()
}

// 处理重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  currentPage.value = 1
  fetchEvaluationData()
}

// 获取评价数据
const fetchEvaluationData = async () => {
  loading.value = true;
  try {
    const params = { ...searchForm }
    const res = await getEvaluationPage(currentPage.value, pageSize.value, params)
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    console.error('获取评价数据失败:', error)
    ElMessage.error('获取评价数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false;
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchEvaluationData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchEvaluationData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add'
  dialogData.value = {}
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getEvaluationDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'edit'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取评价详情失败')
    }
  } catch (error) {
    console.error('获取评价详情失败:', error)
    ElMessage.error('获取评价详情失败')
  }
}

// 处理详情查看
const handleDetail = async (row) => {
  try {
    const res = await getEvaluationDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'view'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取评价详情失败')
    }
  } catch (error) {
    console.error('获取评价详情失败:', error)
    ElMessage.error('获取评价详情失败')
  }
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该评价记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteEvaluation(row.id)
      if (res && res.code === 200) {
        ElMessage.success('删除成功')
        fetchEvaluationData()
        // 刷新统计数据
        fetchRankingData()
        fetchDistributionData()
      } else {
        ElMessage.error(res?.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除评价失败:', error)
      ElMessage.error('删除评价失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 处理弹窗成功
const handleDialogSuccess = () => {
  fetchEvaluationData()
  // 刷新统计数据
  fetchRankingData()
  fetchDistributionData()
}

// 计算表格最大高度
const calculateTableMaxHeight = () => {
  nextTick(() => {
    // 您可以在这里手动微调高度，正值减小高度，负值增加高度
    const manualOffset = 50; 

    const container = document.querySelector('.evaluation-container');
    if (!container) return;

    const containerRect = container.getBoundingClientRect();
    const statisticsSection = container.querySelector('.statistics-section');
    const searchSection = container.querySelector('.search-section');
    const tableHeader = container.querySelector('.table-header');
    const paginationContainer = container.querySelector('.pagination-container');

    const containerTop = containerRect.top;
    const statisticsHeight = statisticsSection ? statisticsSection.offsetHeight : 0;
    const searchHeight = searchSection ? searchSection.offsetHeight : 0;
    const tableHeaderHeight = tableHeader ? tableHeader.offsetHeight : 0;
    const paginationHeight = paginationContainer ? paginationContainer.offsetHeight : 0;

    const availableHeight = window.innerHeight - containerTop - statisticsHeight - searchHeight - tableHeaderHeight - paginationHeight - manualOffset;
    
    tableMaxHeight.value = availableHeight;
  });
};

// 窗口大小改变时重新计算表格最大高度
const handleResize = () => {
  clearTimeout(handleResize.timer);
  handleResize.timer = setTimeout(() => {
    calculateTableMaxHeight();
    if (chartInstance) {
      chartInstance.resize();
    }
  }, 100);
};

// 组件挂载时获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchRankingData(),
      fetchEvaluationData()
    ])
    
    // 初始化图表
    nextTick(() => {
      initDistributionChart()
      fetchDistributionData()
    })
    
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  }
})

// 组件卸载时清理
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize);
  if (handleResize.timer) {
    clearTimeout(handleResize.timer);
  }
});
</script>

<style scoped>
.evaluation-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
  overflow: hidden;
}

/* 统计区域样式 */
.statistics-section {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.chart-card {
  flex: 1;
  background: white;
  border: 1px solid #E4E7ED;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.chart-tabs {
  display: flex;
  gap: 8px;
}

.chart-tabs .el-button {
  padding: 4px 12px;
  font-size: 12px;
  border-radius: 4px;
}

.chart-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-high .legend-dot {
  background-color: #409EFF;
}

.legend-medium .legend-dot {
  background-color: #E6A23C;
}

.legend-low .legend-dot {
  background-color: #F56C6C;
}

.chart-content {
  height: 300px;
}

/* ECharts容器样式 */
.echarts-container {
  width: 100%;
  height: 100%;
}

/* 排名样式 */
.rank-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rank-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #F8F9FA;
  border-radius: 6px;
  border-left: 4px solid #409EFF;
}

.rank-number {
  display: flex;
  align-items: baseline;
  margin-right: 16px;
  min-width: 60px;
}

.rank-index {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.rank-index.rank-top {
  color: #F56C6C;
}

.rank-suffix {
  font-size: 12px;
  color: #999;
  margin-left: 2px;
}

.enterprise-info {
  flex: 1;
}

.enterprise-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.enterprise-industry {
  font-size: 12px;
  color: #666;
}

.score-container {
  font-size: 18px;
  font-weight: bold;
  color: #F56C6C;
}

/* 搜索区域样式 */
.search-section {
  width: 100%;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

.score-high {
  color: #67C23A;
  font-weight: bold;
}

.score-medium {
  color: #E6A23C;
  font-weight: bold;
}

.score-low {
  color: #F56C6C;
  font-weight: bold;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  flex-shrink: 0;
  padding-bottom: 16px;
}

/* 响应式处理 */
@media (max-width: 1200px) {
  .statistics-section {
    flex-direction: column;
  }
  
  .chart-card {
    min-height: 300px;
  }
}

/* 小屏幕适配 */
@media (max-height: 910px) {
  .evaluation-container {
    height: 100vh;
  }
  
  .chart-content {
    height: 220px;
  }
  
  .table-container {
    min-height: 120px;
    overflow-y: auto;
  }
}
</style>