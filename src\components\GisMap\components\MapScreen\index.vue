<template>
    <InitMap/>
    <div id="bubble" class="custom-bubble" v-show="showBubble">
        <div class="bubble-background">
            <div class="bubble-top">
                <a title="关闭模型属性窗体" @click="closeBubble"></a>
                <h3 class="bubble-title">属性信息</h3>
            </div>
            <div class="bubble-container" style="height:21vh;max-height:21vh;overflow:auto;">
                <ul>
                    <li v-for="(value,key,index) in bubbleData.data" :key="index" style="display: flex;">
                        <span style="">{{ key }}: </span>
                        <div class="keyValue" style="display: flex;flex: 2;align-items: center;padding-left: 10px;">
                            {{ value }}
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script setup>
import {nextTick, onMounted, onUnmounted, reactive, ref, watch} from "vue";
import {useRoute} from "vue-router";
import InitMap from "@/components/GisMap/InitMap.vue";
import {defaultCheckedLayers, mapLoaded} from "@/hooks/gishooks";
import {mapStates} from "@/components/GisMap/mapStates";
import {
    alarmLayerMap,
    alarmStatusMap,
    AllPipelineMap, allSpecialModelData,
    associationLayerMap, bridgeModelData,
    defaultShowLayersMap,
    devicesAlarmAll,
    offsetPopupList, pipeModelData,
    requestDataMap
} from "@/components/GisMap/common/gisInfo";
import {gisPopups} from "@/components/GisMap/popup/gisPopup";
import gisDialog from "@/components/GisMap/common/gisDialog";
import Tools from "@/components/GisMap/Tools/index.vue";
import {gisSource} from "@/components/GisMap/common/dataConfig.js";
import bus from "@/utils/mitt";
import {
    formatDataByField,
    formatPolylineDataByFieldAndStyle,
    getIntersectionArray
} from "@/components/GisMap/common/gisUtil";

const route = useRoute();

const eyesEarthActive = ref(false); //透视地图

//模型属性弹框
let showBubble = ref(false);
let bubbleData = reactive({
    data: null
});
//点击单体化
let QingxiePolygons = [];
let QueryResult = {};

const dialogs = ref();
let layerAPIRes = {}; //标记调用接口情况
let layerData = {}; //存储地图容器数据

const state = reactive({
    treeIndex: 1, //默认菜单
    mapType: 'map3d', //地图类型: 电子地图-vector、影像地图-image、气象云图-satellite、气象雷达-radar、三维建模-map3d
});

//点击事件：弹窗1信息展示
const handlepickBillboard = async (entity) => {
    // 关闭弹窗
    if (dialogs.value) {
        dialogs.value.windowClose();
    }
    closeBubble();
    //清除高亮
    mapStates.earth.entity.clearHighlight();
    mapStates.earth.tiles.clearHighlightedModel();
    //查找entity信息是否存在
    const item = layerData[entity.name].find(
        (im) => im.id === entity.id
    );
    if (item) {
        let position = [];
        if (AllPipelineMap.includes(entity.name)) {
            const center = mapStates.earth.entity.getCenterFromPolylineGeometry(item?.geometry);
            position = [center.longitude, center.latitude];
        } else {
            position = [parseFloat(item.longitude), parseFloat(item.latitude)];
        }
        const pitch = state.mapType === 'map3d' ? -45 : -90;
        mapStates.earth.camera.flyTo({
            lon: position[0],
            lat: state.mapType === 'map3d'? position[1] - 0.007 : position[1] + 0.007,
            height: state.mapType === 'map3d'? 1200 : 5000,
            orientation: {
                heading: 0,
                pitch: pitch,
                roll: 0,
            },
        });

        // 在二维模式下，使用直接高亮方法，避免数据源问题
        if (state.mapType === 'image') {
            setTimeout(() => {
                mapStates.earth.entity.highlightEntityDirect(entity);
            }, 500);
        } else {
            mapStates.earth.entity.highlightEntity(entity);
        }

        // 获取地形高程
        const cartographic = Cesium.Cartographic.fromDegrees(position[0], position[1]);
        let height = mapStates.viewer.scene.globe.getHeight(cartographic) || 0;
        // 添加一个小的偏移量确保线条显示在地面上
        height += 4;
        const popupPosition = Cesium.Cartesian3.fromDegrees(position[0], position[1], height);
        const opts = Object.assign({
            viewer: mapStates.viewer,
            position: popupPosition, //弹框位置--笛卡尔坐标；
            gisPopup: gisPopups[entity.name],
            offset: offsetPopupList.includes(entity.name) ? [0, 0.1] : [0, 0.08], // 使用相对值，表示canvas高度的百分比
            useElement: true, //如果增加了el按钮，放开element渲染
            useEcharts: true, //如果增加了echarts图表，放开echarts渲染
            data: {
                ...item,
                layerId: entity.name,
            },
        });
        window.setTimeout(() => {
            dialogs.value = new gisDialog(opts);
        }, 500);
    }
};

/**
 * 清理数据源
 * @param layers
 */
const clearDataSourcesByLayers = (layers) => {
    for (let i = 0; i < layers.length; i++) {
        mapStates.earth.entity.clearDataSourcesEntitiesByLayerId(layers[i]);
    }
};

const handleCheckLayer = (layerId, isChecked) => {
    if (layerId) {
        mapStates.earth.entity.toggleLayerVisibleById(layerId, isChecked);
    }
};

const addPointToMap = (data, layerId) => {
    mapStates.earth.entity.clearDataSourcesEntitiesByLayerId(layerId);
    if (layerId === 'bridge_safety_rating') {
        mapStates.earth.entity.addPointGeometryCustomRender({
            layerId: layerId,
            data: data,
            width: 60, //屏幕分辨率适配
            height: 60, //屏幕分辨率适配
            show: true,
        });
    } else {
        mapStates.earth.entity.addPointGeometryFromDegrees({
            layerId: layerId,
            data: data,
            width: 26, //屏幕分辨率适配
            height: 49, //屏幕分辨率适配
            show: true,
            isCluster: layerId === 'bridge_info' || layerId === 'bridge_safety_rating' ? false : true, //是否聚合
            showLabel: layerId === 'bridge_info' ? true : false // 是否显示标注
        });
    }
};

const addPolylineToMap = (data, layerId) => {
    mapStates.earth.entity.addPolylineGeometryFromDegrees({
        layerId: layerId,
        data: data,
        // material: materialRoadH1,
        width: 4,
        show: true,
    });
};

const addPipeModels = (v) => {
    //添加模型管道
    if (pipeModelData[v]) {
        pipeModelData[v].forEach((id) => {
            mapStates.earth.basemap.addPipeLineModel(id, gisSource.pipeLine[id]);
        })
    }
}

const addBridgeModels = (v) => {
    //加载桥梁模型
    if (bridgeModelData[v]) {
        bridgeModelData[v].forEach((id) => {
            mapStates.earth.basemap.addBridgeModel(id, gisSource.maxModel[id]);
        })
    }
}

//删除各专项模型
const clearAllSpecialModels = () => {
    mapStates.earth.basemap.removeModelLayerByNames(allSpecialModelData, false); //桥梁模型
}

const showPipeline = async (v) => {
    if (state.mapType === 'map3d') {
        addPipeModels(v); // 三维模式-添加模型管道
    } else {
        if (layerAPIRes[v] === v) {
            handleCheckLayer(v, true); // 二维模式-显示二维管线
        } else {
            try {
                layerAPIRes[v] = v; // 标记接口已调用
                const params = requestDataMap[v]['params']; // 使用默认参数
                // 调用接口获取数据
                const res = await requestDataMap[v]['api'](params);
                if (res?.data) {
                    let gisData;
                    // 如果有过滤条件，过滤数据
                    if (requestDataMap[v]['filterList']) {
                        const devices = res.data.filter((item) =>
                            requestDataMap[v]['filterList'].includes(item[requestDataMap[v]['filterColumn']])
                        ); // 根据过滤列过滤数据
                        gisData = formatPolylineDataByFieldAndStyle(devices, "gisType", v); // 格式化数据
                    } else {
                        gisData = formatPolylineDataByFieldAndStyle(res.data, "gisType", v); // 格式化数据
                    }
                    layerData[v] = gisData; // 存储图层数据
                    addPolylineToMap(gisData, v); // 添加点到地图

                    bus.emit("searchDataChanged", layerData); // 触发数据变化事件
                } else {
                    layerData[v] = []; // 存储图层数据
                    bus.emit("searchDataChanged", layerData); // 触发数据变化事件
                    console.warn(`图层 ${v} 返回的数据为空`);
                }
            } catch (error) {
                console.error(`加载管线 ${v} 时发生错误:`, error);
            }
        }
    }
}

const getGisData = async (typeList) => {
    // 创建typeList的副本，避免修改原始参数
    let processTypeList = [...typeList];
    //  判断类型列表是否包含 "_alarm"
    const hasAlarmItems = typeList.some(item => item.includes('_alarm'));
    // 报警参数，初始化为空数组
    const alarms = {
        gas_alarm: [], // 燃气报警状态
        drainage_alarm: [], // 排水报警状态
        heating_alarm: [], // 供暖报警状态
        bridge_alarm: [] // 桥梁报警状态
    };
    // 获取已勾选的设备图层
    const associations = {
        gas_alarm: getIntersectionArray(processTypeList, associationLayerMap['gas_alarm']), // 燃气相关图层
        drainage_alarm: getIntersectionArray(processTypeList, associationLayerMap['drainage_alarm']), // 排水相关图层
        heating_alarm: getIntersectionArray(processTypeList, associationLayerMap['heating_alarm']), // 供暖相关图层
        bridge_alarm: getIntersectionArray(processTypeList, associationLayerMap['bridge_alarm']),  // 桥梁相关图层
    };
    // 如果类型列表包含 "_alarm"，处理报警相关逻辑
    if (hasAlarmItems) {
        // 遍历报警图层映射，处理报警状态
        Object.entries(alarmLayerMap).forEach(([key, layers]) => {
            layers.forEach((layer) => {
                // 如果报警状态映射中存在该图层，并且该图层在类型列表中
                if (alarmStatusMap[layer] && processTypeList.includes(layer)) {
                    alarms[key].push(alarmStatusMap[layer]); // 添加报警状态
                }
            });

            // 如果有报警状态,但没有勾选关联图层，要强制勾选报警对应的所有设备图层；
            if (alarms[key].length > 0 && associations[key].length === 0) {
                processTypeList = processTypeList.concat(associationLayerMap[key]);
            } else if (alarms[key].length > 0 && associations[key].length > 0) {
                // 如果有报警状态且勾选了关联图层，清除没有勾选的关联图层；
                const unAssociationLayer = associationLayerMap[key].filter(layer => !associations[key].includes(layer));
                clearDataSourcesByLayers(unAssociationLayer); // 清除未勾选的关联图层
                // 如果有报警状态且勾选了关联图层，将关联图层添加到类型列表中
                processTypeList = processTypeList.concat(associations[key]);
            }
        });
    } else {
        // todo 如果没有报警图层，获取已勾选的设备图层不包含监测设备的关联图层，则清除监测设备图层
        Object.entries(associations).forEach(([key, layers]) => {
            if (layers.length === 0) {
                clearDataSourcesByLayers(associationLayerMap[key])
            }
        });
    }
    // 遍历类型列表，处理每种类型
    for (const v of processTypeList) {
        //  如果是管线，处理管线相关逻辑
        if (AllPipelineMap.includes(v)) {
            await showPipeline(v);
            continue;
        } else if (!requestDataMap[v]) {
            // console.warn(`未找到图层 ${v} 的配置信息`);
            continue;
        }
        // 三维模式-显示桥梁模型，同时也显示二维桥梁点位
        if (v === 'bridge_info' && state.mapType === 'map3d') {
            addBridgeModels(v);
        }
        try {
            // 检查某类型是否已调用过接口（监测设备除外）
            if (layerAPIRes[v] === v && !devicesAlarmAll.includes(v)) {
                handleCheckLayer(v, true); // 显示图层
                continue;
            }
            layerAPIRes[v] = v; // 标记接口已调用
            let params = {};
            // 如果是监测设备，设置接口参数
            if (devicesAlarmAll.includes(v)) {
                params = {
                    ...requestDataMap[v]['params'], // 合并默认参数
                    alarmStatuses: v.includes('gas') ? alarms.gas_alarm :
                        v.includes('drainage') ? alarms.drainage_alarm :
                            v.includes('heating') ? alarms.heating_alarm :
                                v.includes('bridge') ? alarms.bridge_alarm : [], // 根据类型设置报警状态
                };
            } else {
                params = requestDataMap[v]['params']; // 非监测设备使用默认参数
            }
            // 调用接口获取数据
            const res = await requestDataMap[v]['api'](params);
            if (res?.data) {
                let gisData;
                // 如果有过滤条件，过滤数据
                if (requestDataMap[v]['filterList']) {
                    const devices = res.data.filter((item) =>
                        requestDataMap[v]['filterList'].includes(item[requestDataMap[v]['filterColumn']])
                    ); // 根据过滤列过滤数据
                    gisData = formatDataByField(devices, "gisType", v); // 格式化数据
                } else {
                    gisData = formatDataByField(res.data, "gisType", v); // 格式化数据
                }
                layerData[v] = gisData; // 存储图层数据
                addPointToMap(gisData, v); // 添加点到地图

                bus.emit("searchDataChanged", layerData); // 触发数据变化事件
            } else {
                layerData[v] = []; // 存储图层数据
                bus.emit("searchDataChanged", layerData); // 触发数据变化事件
                console.warn(`图层 ${v} 返回的数据为空`);
            }
        } catch (error) {
            console.error(`加载图层 ${v} 数据时发生错误:`, error);
        }
    }
};

const optimizeImageryLayer = () => {
    // 获取当前的图层
    let layer = mapStates.viewer.scene.imageryLayers.get(0);
    // 改变当前地图的组织结构
    layer.minificationFilter = Cesium.TextureMinificationFilter.NEAREST;
    layer.magnificationFilter = Cesium.TextureMagnificationFilter.NEAREST;
};

const resetPostion = () => {
    // 根据地图模式设置不同的相机高度
    const height = state.mapType === 'map3d' ? 7000 : 12000;
    const pitch = state.mapType === 'map3d' ? -45 : -90;
    const lat = state.mapType === 'map3d' ? 35.221 : 35.221 + 0.07;

    mapStates.earth.camera.flyTo({
        lon: 115.097,
        lat: lat,
        height: height,
        orientation: {
            heading: 0,
            pitch: pitch,
            roll: 0,
        },
    });
};

const addCimModelMap = () => {
    if (import.meta.env.VITE_GIS_MAP_ID === "pro") {
        console.log("生产环境");
        mapStates.earth.basemap.addOsgbModel(gisSource.osgb.dongMing);
    } else if (import.meta.env.VITE_GIS_MAP_ID === "dev") {
        console.log("开发环境");
        mapStates.earth.basemap.addOsgbModel(gisSource.osgb.dongMing);
    }
};

// 监听路由变化，清除地图数据，初始话地图数据
const onPageIndex = (path) => {
    CancelQingxiePolyHight();
    closeBubble();
    mapStates.earth.tiles.clearHighlightedModel();
    //清除高亮
    mapStates.earth.entity.clearHighlight();
    // 关闭弹窗
    if (dialogs.value) {
        dialogs.value.windowClose();
    }
    // 清除各专项模型
    clearAllSpecialModels();
    // 清除数据源
    const layerIds = [...Object.keys(layerData), ...Object.keys(layerAPIRes)];
    clearDataSourcesByLayers([...new Set(layerIds)]);
    resetPostion();
    layerData = {};
    layerAPIRes = {};
    if (defaultShowLayersMap[path]) {
        defaultCheckedLayers.value = defaultShowLayersMap[path];
        getGisData(defaultCheckedLayers.value);
    } else {
        defaultCheckedLayers.value = [];
    }
};

//start 模型点击事件
const clickModel = () => {
    let handler = new Cesium.ScreenSpaceEventHandler(mapStates.viewer.scene.canvas);
    handler.setInputAction(function (click) {
        /* console.log(mapStates.viewer.camera.position);
         console.log(mapStates.viewer.camera.heading)
         console.log(mapStates.viewer.camera.pitch)
         console.log(mapStates.viewer.camera.roll)*/
        // if (eyesEarthActive.value) {
        CancelQingxiePolyHight();
        const pickingEntity = mapStates.viewer.scene.pick(click.position);
        //判断选择是否为Cesium3DTileFeature
        if (!Cesium.defined(pickingEntity)) {
            return; // 没有拾取
        }
        if (pickingEntity) {
            // 关闭弹窗
            if (dialogs.value) {
                dialogs.value.windowClose();
            }
            closeBubble();
            //清除高亮
            mapStates.earth.entity.clearHighlight();
            mapStates.earth.tiles.clearHighlightedModel();
            const cartesian = mapStates.viewer.scene.pickPosition(click.position);
            // var cartographic = Cesium.Cartographic.fromCartesian(cartesian);
            // QueryResult.lontable = Cesium.Math.toDegrees(cartographic.longitude); //四舍五入 小数点后保留五位
            // QueryResult.lattable = Cesium.Math.toDegrees(cartographic.latitude); //四舍五入  小数点后保留五位
            // QueryResult.height = cartographic.height;
            let s = mapStates.viewer.scene.pickGlobe(click.position);
            if (s) {
                let a = mapStates.viewer.scene.globe.ellipsoid.cartesianToCartographic(s);
                QueryResult.lontable = (a.longitude * 180 / Math.PI).toFixed(6);
                QueryResult.lattable = (a.latitude * 180 / Math.PI).toFixed(6);
                QueryResult.height = a.height.toFixed(3);
            }
            QueryResult.cartesian = cartesian;
            pickingEntity.queryResult = QueryResult;
            //  判断是否为管道
            if (pickingEntity instanceof Cesium.Cesium3DTileFeature || pickingEntity instanceof Cesium.Cesium3DTilePointFeature) {
                // 判断是否为管线
                selectSingle(pickingEntity, QueryResult);
            } else if (
                pickingEntity &&
                pickingEntity.id instanceof Cesium.Entity &&
                pickingEntity.id.billboard
            ) {
                handlepickBillboard(pickingEntity.id);
            } else if (
                pickingEntity &&
                pickingEntity.id instanceof Cesium.Entity &&
                pickingEntity.id.polyline
            ) {
                handlepickBillboard(pickingEntity.id);
            } else if (
                pickingEntity &&
                pickingEntity.id instanceof Cesium.Entity &&
                pickingEntity.id.label
            ) {
                // todo 待完善标注点击事件
            } else if (typeof pickingEntity === "undefined") {
                // 判断是否为undefined
            } else {
                // todo 默认处理
            }
        }
        // }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
}
const selectSingle = (pickingEntity, QueryResult) => {
    // let selectPipe = result;
    let queryID = pickingEntity.getProperty("gw_id");
    if (!queryID) return;

    let queryUUID = pickingEntity.primitive.properties.gw_layer_guid;

    let queryCondition = new CommonService.QueryCondition();
    queryCondition.addPropertyCondition(
        CommonService.SqlOperator.AND,
        "us_id",
        CommonService.SqlOperator.EQUAL,
        queryID
    );

    CommonService.Query.queryProperty(
        gisSource.serverApi.commonService,
        queryUUID,
        queryCondition
    ).then(function (result) {
        if (result._records && result._records.length > 0) {
            //闪烁属性
            mapStates.earth.tiles.highlightedModel(pickingEntity)
            //弹窗
            // ShowOSGBText(result._records,QueryResult);
            delete result._records[0].geometry;
            // delete result._records[0].gw_layer;
            delete result._records[0].gw_layer_user;
            showBubbleText(result._records[0], QueryResult.cartesian)
        }
    })
    // let queryCondition =  new CommonService.QueryCondition();
    //   queryCondition.setAsCircle(pickingEntity.queryResult.lontable, pickingEntity.queryResult.lattable, pickingEntity.queryResult.height, 1.0);
    //   CommonService.Query.queryProperty(window.queryIP, pickingEntity.primitive.properties.gw_layer_guid, queryCondition)
    //       .then(function (result) {
    //               if (result._records&&result._records.length>0) {
    //                   //闪烁属性
    //                   HilightOsgbFeature(result._records[0].geometry.coordinates);
    //                   //弹窗
    //                   // ShowOSGBText(result._records,QueryResult);
    //                   delete result._records[0].geometry;
    //                   // delete result._records[0].gw_layer;
    //                   delete result._records[0].gw_layer_user;
    //                   showBubbleText(result._records[0],QueryResult.cartesian)
    //               }
    //       })
}
const showBubbleText = (data, cartesian) => {
    showBubble.value = true;
    bubbleData.data = formatAttributes(data);
    const bubble = document.querySelector("#bubble");
    // bubble.style.display = "block";
    const bubbleBackground = document.querySelector(".bubble-background");
    let posi = cartesian;
    //每帧渲染结束监听
    mapStates.viewer.scene.postRender.addEventListener(function (e) {
        if (posi) {
            const winpos = mapStates.viewer.scene.cartesianToCanvasCoordinates(posi);
            if (winpos) {
                bubble.style.left = winpos.x + 80 - bubbleBackground.clientWidth / 2 + "px";
                bubble.style.top = winpos.y - 50 - bubbleBackground.clientHeight + "px";
            }
        }
    });
}
const closeBubble = () => {
    mapStates.earth.tiles.clearHighlightedModel();
    showBubble.value = false;
    bubbleData.data = null;
    mapStates.viewer.scene.postRender.removeEventListener(showBubbleText)
}
// 取消倾斜摄影高亮
const CancelQingxiePolyHight = () => {
    if (QingxiePolygons && QingxiePolygons.length > 0) {
        for (let num in QingxiePolygons) {
            mapStates.viewer.entities.remove(QingxiePolygons[num]);
        }
        QingxiePolygons = [];
    }
}

// 格式化属性字段
const formatAttributes = (attributes) => {
    // console.log(attributes)
    let obj = {};
    // if(attributes.gw_layer_type){//管线
    //   obj.type='pipeline';
    if (attributes['起点点号']) {
        obj['起点点号'] = attributes['起点点号'];
        obj['终点点号'] = attributes['终点点号'];
        obj['起点埋深'] = attributes['起点埋深'];
        obj['终点埋深'] = attributes['终点埋深'];
        obj['起点高程'] = attributes['起点高程'];
        obj['终点高程'] = attributes['终点高程'];
        obj['管径'] = attributes['管径'];
        obj['材质'] = attributes['材质'];
        obj['埋设方式'] = attributes['埋设方式'];
        obj['使用状况'] = attributes['使用状况'];
        obj['类型'] = '管线';
    } else {
        obj['管点编号'] = attributes['管点编号'];
        obj['井盖材质'] = attributes['井盖材质'];
        obj['井盖规格'] = attributes['井盖规格'];
        obj['井底埋深'] = attributes['井底埋深'];
        obj['附属物'] = attributes['附属物'];
        obj['地面高程'] = attributes['地面高程'];
        obj['类型'] = '管点';
    }
    return obj;
}
//end 模型点击事件

const InitScreen = () => {
    // 添加默认底图--二维模式
    mapStates.earth.basemap.add("tdt_img");
    mapStates.earth.basemap.add("tdt_cia");
    mapStates.earth.basemap.add("tdt_terrain");
    // 优化影像图层
    optimizeImageryLayer();
    if (state.mapType ==='map3d') {
        mapStates.viewer.scene.morphTo3D(0);
        addCimModelMap();
    } else {
        mapStates.viewer.scene.morphTo2D(0);
    }
    // 重置位置
    resetPostion();
    //模型点击事件注册
    clickModel();

    // 最小缩放高度（米）
    // mapStates.viewer.scene.screenSpaceCameraController.minimumZoomDistance = 500;
    // 最大缩放高度（米）
    // mapStates.viewer.scene.screenSpaceCameraController.maximumZoomDistance = 80000;
};

bus.on('showBridgeDetailPosition', (data) => {
    // 关闭弹窗
    if (dialogs.value) {
        // 只允许一个弹窗出现
        dialogs.value.windowClose();
    }
    //清除高亮
    mapStates.earth.entity.clearHighlight();

    if (!data?.layerId || !data?.id) return;
    // 获取地形高程
    const cartographic = Cesium.Cartographic.fromDegrees(data?.longitude, data?.latitude);
    let height = mapStates.viewer.scene.globe.getHeight(cartographic) || 0;
    // 添加一个小的偏移量确保线条显示在地面上
    height += 4;
    mapStates.earth.camera.flyTo({
        lon: data?.longitude,
        lat: data?.latitude - 0.001,
        height: height + 100,
        orientation: {
            heading: 0,
            pitch: -45,
            roll: 0,
        },
    });
})

// 手动切换底图类型
bus.on('changeBottomMapType', (type) => {
    state.mapType = type;
    switch (type) {
        case 'image':
            //todo 需要处理加载的三维模型和cim模型
            clearAllSpecialModels();
            if (mapStates.earth.basemap.osgbLayer) {
                mapStates.earth.basemap.osgbLayer.show = false;
            }
            mapStates.viewer.scene.morphTo2D(0);
            // 在二维模式下，强制刷新地图渲染，确保点位能够正确显示
            setTimeout(() => {
                mapStates.viewer.scene.requestRender();
            }, 300);
            break;
        case 'map3d':
            //todo 需要加载三维模型和cim模型
            mapStates.viewer.scene.morphTo3D(0);
            addCimModelMap();
            break;
        case 'vector':
            //todo 待完善
            // mapStates.earth.basemap.add("tdt_vec");
            // mapStates.earth.basemap.add("tdt_cva");
            // mapStates.viewer.scene.morphTo2D(0);
            break;
        case 'satellite':
            //todo 待完善
            break;
        case 'radar':
            //todo 待完善
            break;
    }
    onPageIndex(route.path); //重置地图数据
})

// 透视地图
bus.on("activeEyesEarth", (active) => {
    eyesEarthActive.value = active;
});

//关闭弹框
bus.on("resetGisPopup", () => {
    mapStates.earth.entity.clearHighlight();
    if (dialogs.value) {
        dialogs.value.windowClose();
    }
});

bus.on('screenTableRowFocusToGisPoint', (params) => {
    const {specialType, deviceId, deviceType} = params;
    let layerQueryId = null;
    // 1. 先筛选专项类型相关的图层
    const prefix = specialType + '_'; // 如 gas_、drainage_、heating_、bridge_
    for (const [key, value] of Object.entries(requestDataMap)) {
        if (key.startsWith(prefix) && value.params && Array.isArray(value.params.deviceTypes)) {
            if (value.params.deviceTypes.includes(deviceType)) {
                layerQueryId = key;
                break;
            }
        }
    }
    if (!layerQueryId || !deviceId) return;

    //如果当前图层未加载，先加载图层
    if (defaultCheckedLayers.value.includes(layerQueryId)) {
        mapStates.earth.entity.toggleLayerVisibleById(layerQueryId, true);
    } else {
        defaultCheckedLayers.value = [...defaultCheckedLayers.value, layerQueryId];
    }
    window.setTimeout(() => {
        //选中列表某一行，触发地图定位功能。
        const selectEntity = mapStates.earth.entity.getEntityFromTargetLayerById(layerQueryId, deviceId)
        if (selectEntity) {
            handlepickBillboard(selectEntity)
        }
    }, 500);
})

//搜索交互大屏地图定位
bus.on('screenSlideFocusToGisPoint', (params) => {
    const {layerQueryId, id} = params;
    if (!layerQueryId || !id) return;

    //如果当前图层未加载，先加载图层
    if (defaultCheckedLayers.value.includes(layerQueryId)) {
        mapStates.earth.entity.toggleLayerVisibleById(layerQueryId, true);
    } else {
        defaultCheckedLayers.value = [...defaultCheckedLayers.value, layerQueryId];
    }

    window.setTimeout(() => {
        //选中列表某一行，触发地图定位功能。
        const selectEntity = mapStates.earth.entity.getEntityFromTargetLayerById(layerQueryId, id)
        if (selectEntity) {
            handlepickBillboard(selectEntity)
        }
    }, 500);
})

watch(
    () => [mapLoaded.value],
    (val) => {
        if (val) {
            nextTick(() => {
                InitScreen();
            });
        }
    },
    {
        deep: true,
    }
);

watch(
    () => [mapLoaded.value, route],
    (val) => {
        if (val) {
            nextTick(() => {
                eyesEarthActive.value = false; //重置透视地图状态
                resetPostion(); //重置位置
                onPageIndex(route.path); //重置地图数据
            });
        }
    },
    {
        deep: true,
    }
);

watch(
    () => defaultCheckedLayers.value,
    (val) => {
        // console.log("defaultCheckedLayers.value--22222-->>", val);
        getGisData(val);
    },
    {
        deep: true,
    }
);

onMounted(() => {
});
onUnmounted(() => {
});
</script>

<style lang="scss" scoped>
.map-screen {
  width: 100%;
  height: 100%;
}

.bubble-container::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 3px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 4px;
  scrollbar-arrow-color: red;
}

.bubble-container::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.5);
}

.bubble-container::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}

.bubble-top {
  height: 35px;
  line-height: 35px;
  background: url("@/assets/images/gis/bubble/bubbleTitle.png") no-repeat center 15px;
}

.bubble-top span {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  float: left;
  text-indent: 20px;
}

.bubble-top a {
  display: block;
  background: url("@/assets/images/gis/bubble/bubbleClose.png") no-repeat;
  width: 22px;
  height: 22px;
  float: right;
  margin-right: 4px;
  margin-top: 5px;
  cursor: pointer;
}

.custom-bubble {
  position: absolute;
  z-index: 4;
}

.bubble-background {
  width: 301px;
  height: 279px;
  background: url("@/assets/images/gis/bubble/bubbleBg.png") no-repeat;
}

.bubble-container {
  margin-top: 10px;
  color: #fff;
}

.bubble-container ul {
  margin-left: 10px;
  margin-right: 10px;
  display: flex;
  flex-wrap: wrap;
  padding-left: 40px;
}

.bubble-container ul > li {
  width: 90%;
  text-align: center;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  /* padding-left: 5px; */
  padding-right: 5px;
  cursor: pointer;
  border-top: 1px solid rgba(34, 189, 142, 0.2);
  padding-bottom: 8px;
  padding-top: 8px;
}

.bubble-container ul > li:hover {
  white-space: normal;
  overflow: auto;
}

.bubble-container ul > li > a {
  display: block;
}

.bubble-container ul > li > span {
  display: block;
  color: rgb(34, 189, 142);
  float: left;
}

.bubble-title {
  text-align: center;
  color: rgb(34, 189, 142);
  margin-left: 27px;
  font-size: 1em;
  padding-top: 5px;
}
</style>
